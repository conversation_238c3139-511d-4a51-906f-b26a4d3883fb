# Databricks Reranker Test Script
# This PowerShell script runs the Databricks reranker test script

Write-Host "Databricks Reranker Model Test" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan

Write-Host "You will be prompted to enter your Databricks API token." -ForegroundColor Yellow

# Run the Python script
python databricks_reranker.py

# Pause to see the results
Write-Host "`nPress any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
