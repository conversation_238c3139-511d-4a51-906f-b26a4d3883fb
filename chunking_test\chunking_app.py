"""
Chunking Strategy Evaluation App

This Streamlit app allows users to evaluate different chunking strategies for RAG applications.
It lets users upload documents, apply different chunking strategies, and compare retrieval results.
"""

import os
import sys
import json
import time
import streamlit as st
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
import fitz  # PyMuPDF
from pathlib import Path

# No longer using CrossEncoder for reranking, using Databricks reranker instead
has_cross_encoder = False

# Try to import Databricks reranker
try:
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
    from src.keyword_code.models.databricks_reranker import load_databricks_reranker_model
    has_databricks_reranker = True
except ImportError:
    has_databricks_reranker = False
    print("Warning: Databricks reranker not available. Will use local reranker if available.")

# Set page configuration - MUST be the first Streamlit command
st.set_page_config(
    page_title="Chunking Strategy Evaluation",
    page_icon="📄",
    layout="wide"
)

# Add parent directory to path to import from main project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import from chunking_test module
try:
    from chunking_test.chunking_test import (
        extract_text_from_pdf,
        ChunkingStrategy,
        SentenceChunker,
        HybridSectionChunker,
        RollingWindowChunker,
        ProductionChunker,
        ProductionV2Chunker,
        FixedSizeChunker,
        RecursiveChunker,
        SemanticChunker,
        LlamaIndexSemanticChunker,
        LegalRecursiveSemanticChunker,
        SemanticDoubleMergingChunker,
        perform_bm25_search,
        count_tokens,
        RESULTS_DIR
    )
    from chunking_test.enhanced_retrieval import EnhancedRetrieval
except ImportError:
    # Try alternative import path
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    try:
        from chunking_test import (
            extract_text_from_pdf,
            ChunkingStrategy,
            SentenceChunker,
            HybridSectionChunker,
            RollingWindowChunker,
            ProductionChunker,
            ProductionV2Chunker,
            FixedSizeChunker,
            RecursiveChunker,
            SemanticChunker,
            LlamaIndexSemanticChunker,
            LegalRecursiveSemanticChunker,
            SemanticDoubleMergingChunker,
            perform_bm25_search,
            count_tokens,
            RESULTS_DIR
        )
        from enhanced_retrieval import EnhancedRetrieval
    except ImportError:
        st.error("Error: Could not import chunking strategies. Make sure chunking_test.py is in the current directory.")
        st.stop()

# Import local embedding model
try:
    from embedding_model import load_databricks_embedding_model
    from sentence_transformers import SentenceTransformer
except ImportError:
    st.error("Could not import embedding model. Make sure embedding_model.py is in the current directory.")
    st.stop()

# Load the embedding model
@st.cache_resource
def get_embedding_model():
    """Load and cache the embedding model (Databricks or SentenceTransformer fallback)"""
    model = load_databricks_embedding_model()
    if model is None:
        st.warning("Databricks embedding model failed to load. Using SentenceTransformer as fallback.")
        try:
            model = SentenceTransformer("all-MiniLM-L6-v2")
        except Exception as e:
            st.error(f"Failed to load SentenceTransformer fallback: {e}")
            st.stop()
    return model

# Load the reranker model (if available)
@st.cache_resource
def get_reranker_model():
    """Load and cache the Databricks reranker model for reranking"""
    # Load Databricks reranker if available
    if has_databricks_reranker:
        try:
            print("Loading Databricks reranker model...")
            model = load_databricks_reranker_model()
            if model:
                print("Databricks reranker model loaded successfully.")
                return model
            else:
                print("Failed to load Databricks reranker model. Reranking will be disabled.")
                return None
        except Exception as e:
            print(f"Error loading Databricks reranker model: {e}")
            print("Reranking will be disabled.")
            return None
    else:
        print("Databricks reranker not available. Reranking will be disabled.")
        return None

# Initialize models
embedding_model = get_embedding_model()
reranker_model = get_reranker_model()

# Create a directory for uploaded files if it doesn't exist
UPLOAD_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "uploads")
os.makedirs(UPLOAD_DIR, exist_ok=True)

# Create results directory if it doesn't exist
os.makedirs(RESULTS_DIR, exist_ok=True)

# Custom CSS
st.markdown("""
<style>
    .main .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
    }
    .stTabs [data-baseweb="tab"] {
        height: 50px;
        white-space: pre-wrap;
        background-color: #f0f2f6;
        border-radius: 4px 4px 0px 0px;
        gap: 1px;
        padding-top: 10px;
        padding-bottom: 10px;
    }
    .stTabs [aria-selected="true"] {
        background-color: #e6f0ff;
    }
    .stMarkdown h3 {
        margin-top: 0.8rem;
        margin-bottom: 0.8rem;
    }
    .result-container {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 10px;
    }
    .metrics-container {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }
    .metric-box {
        background-color: #f0f2f6;
        border-radius: 5px;
        padding: 10px;
        flex: 1;
        min-width: 120px;
        text-align: center;
    }
    .scrollable-container {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
    }

    /* Styling for chunk containers */
    .stTextArea textarea {
        font-family: monospace;
        font-size: 14px;
        line-height: 1.5;
    }

    /* Make the chunk text areas read-only looking */
    .stTextArea textarea:focus {
        box-shadow: none !important;
        border-color: #ddd !important;
    }

    /* Style the chunk metadata */
    .chunk-metadata {
        font-size: 0.9em;
        color: #666;
        margin-bottom: 5px;
    }

    /* Style for legal element metadata */
    .legal-element {
        color: #2E8B57;
        font-weight: bold;
        margin-right: 15px;
    }

    /* Style for section type metadata */
    .section-type {
        color: #4682B4;
        margin-right: 15px;
    }

    /* Style for definition indicators */
    .definition-indicator {
        color: #B22222;
        font-weight: bold;
    }

    /* Style the chunk separators */
    hr {
        margin-top: 10px;
        margin-bottom: 20px;
        border: 0;
        border-top: 1px dashed #ccc;
    }
</style>
""", unsafe_allow_html=True)

# Title and description
st.title("Chunking Strategy Evaluation")
st.markdown("""
This app allows you to evaluate different chunking strategies for RAG applications.
Upload a document, apply different chunking strategies, and compare retrieval results.

### Special Features for Legal Documents
This app includes a specialized **Legal Recursive-Semantic Chunking** strategy that combines:
- Recursive chunking to preserve document structure
- Semantic analysis to group related concepts
- Special handling for legal elements like definitions, citations, and numbered clauses
- Prioritization of definitional chunks for improved retrieval

This hybrid approach is designed specifically for legal documents like contracts, agreements, and regulations.
""")

# Sidebar for configuration
with st.sidebar:
    st.header("Configuration")

    # Chunking strategies selection
    st.subheader("Chunking Strategies")
    use_fixed_size = st.checkbox("Fixed-size Chunking", value=True)
    use_sentence_based = st.checkbox("Sentence-based Chunking", value=True)
    use_recursive = st.checkbox("Recursive Chunking", value=True)
    use_rolling_window = st.checkbox("Rolling Window Chunking", value=True)
    use_hybrid = st.checkbox("Hybrid Section Chunking", value=True)
    use_production = st.checkbox("Production Chunking", value=True)
    use_production_v2 = st.checkbox("Production V2 Chunking", value=True, help="Enhanced production chunker with legal document metadata tagging")
    use_legal_recursive = st.checkbox("Legal Recursive-Semantic Chunking", value=True, help="Specialized hybrid chunker for legal documents that combines recursive structure with semantic analysis")
    use_llama_index = st.checkbox("LlamaIndex Semantic Chunking", value=True, help="Specialized chunker for legal documents that uses LlamaIndex for semantic analysis")
    use_semantic_double_merging = st.checkbox("Semantic Double Merging Chunking", value=True, help="LlamaIndex's two-pass semantic chunking that merges similar content and appends small chunks")

    # Search configuration
    st.subheader("Search Configuration")
    search_method = st.radio(
        "Search Method",
        ["BM25", "Semantic", "Hybrid"],
        index=2
    )

    top_k = st.slider("Number of results to retrieve", min_value=3, max_value=20, value=10)

    # Hybrid search parameters (if hybrid selected)
    if search_method == "Hybrid":
        alpha = st.slider("Hybrid alpha (BM25 weight)", min_value=0.0, max_value=1.0, value=0.5, step=0.1)
    else:
        alpha = 0.5  # Default value

# Main content area with tabs
tab1, tab2, tab3, tab4 = st.tabs(["📄 Document Upload", "🔍 Chunking Analysis", "🤖 Retrieval Simulation", "⚖️ Rerank Simulation"])

# Document Upload Tab
with tab1:
    col1, col2 = st.columns([2, 1])

    with col1:
        st.header("Upload Document")

        uploaded_file = st.file_uploader("Choose a PDF or text file", type=["pdf", "txt"])

    with col2:
        st.header("About Chunking Strategies")
        with st.expander("Fixed-size Chunking"):
            st.markdown("""
            Splits text into chunks of fixed token length with optional overlap.

            **Good for**: Simple documents with consistent content.

            **Limitations**: May split sentences mid-thought, losing context.
            """)

        with st.expander("Sentence-based Chunking"):
            st.markdown("""
            Groups a fixed number of sentences into each chunk.

            **Good for**: Preserving sentence-level meaning.

            **Limitations**: Sentences vary in length, leading to inconsistent chunk sizes.
            """)

        with st.expander("Recursive Chunking"):
            st.markdown("""
            Recursively splits text using a hierarchy of delimiters (paragraphs → sentences).

            **Good for**: Respecting document structure.

            **Limitations**: May not capture semantic relationships between parts.
            """)

        with st.expander("Rolling Window Chunking"):
            st.markdown("""
            Creates overlapping chunks by sliding a window over the text.

            **Good for**: Ensuring context is preserved across chunk boundaries.

            **Limitations**: Creates many redundant chunks.
            """)

        with st.expander("Hybrid Section Chunking"):
            st.markdown("""
            Uses different strategies for different document sections.

            **Good for**: Documents with varied sections (e.g., definitions vs. articles).

            **Limitations**: Requires section identification heuristics.
            """)

        with st.expander("Production Chunking"):
            st.markdown("""
            Uses spaCy for sentence segmentation, groups N sentences per chunk.

            **Good for**: Production-ready, consistent chunking.

            **Limitations**: Doesn't consider document structure or semantics.
            """)

        with st.expander("Production V2 Chunking ⭐"):
            st.markdown("""
            Extends the production chunker with legal document metadata tagging.

            **Good for**: Production environments requiring legal document awareness.

            **Features**:
            - Identifies legal elements (definitions, references, clauses)
            - Tags each chunk with section type
            - Maintains the simplicity of sentence-based chunking
            - Adds rich metadata for filtering and retrieval
            """)

        with st.expander("LlamaIndex Semantic Chunking ⭐"):
            st.markdown("""
            Inspired by LlamaIndex's SemanticSplitterNodeParser, this chunker splits text at semantic breakpoints.

            **Good for**: Preserving semantic coherence in documents where topics shift.

            **Features**:
            - Detects natural topic boundaries using embeddings
            - Adaptive chunk sizes based on content semantics
            - Prevents cutting through semantically related sentences
            - Identifies breakpoints based on similarity percentile threshold
            """)

        with st.expander("Semantic Double Merging Chunking ⭐"):
            st.markdown("""
            Implements LlamaIndex's SemanticDoubleMergingSplitterNodeParser for advanced semantic chunking.

            **Good for**: Documents where both semantic coherence and context preservation are critical.

            **Features**:
            - Two-pass merging process for optimal chunks
            - First merges similar adjacent sentences
            - Then appends small chunks to their most similar neighbors
            - Maintains semantic coherence while optimizing chunk sizes
            - Better preserves context across related sections
            """)

        with st.expander("Legal Recursive-Semantic Chunking ⭐"):
            st.markdown("""
            Combines recursive chunking with semantic refinement and legal-specific handling.

            **Good for**: Legal documents where definitions and context are critical.

            **Features**:
            - Preserves definitions and references
            - Respects legal document structure
            - Groups semantically related content
            - Special handling for citations and clauses
            """)

    if uploaded_file is not None:
        # Save the uploaded file
        file_path = os.path.join(UPLOAD_DIR, uploaded_file.name)
        with open(file_path, "wb") as f:
            f.write(uploaded_file.getvalue())

        st.success(f"File uploaded: {uploaded_file.name}")

        # Extract text from the file
        if uploaded_file.name.lower().endswith('.pdf'):
            with st.spinner("Extracting text from PDF..."):
                full_text, page_texts = extract_text_from_pdf(file_path)
        else:  # Text file
            with st.spinner("Reading text file..."):
                with open(file_path, 'r', encoding='utf-8') as f:
                    full_text = f.read()
                    page_texts = {0: full_text}  # Simulate page text

        # Store in session state
        st.session_state.full_text = full_text
        st.session_state.page_texts = page_texts
        st.session_state.file_path = file_path
        st.session_state.file_name = uploaded_file.name

        # Display text statistics
        st.subheader("Document Statistics")
        col1, col2, col3 = st.columns(3)
        col1.metric("Characters", f"{len(full_text):,}")
        col2.metric("Tokens (approx)", f"{count_tokens(full_text):,}")
        col3.metric("Pages", f"{len(page_texts)}" if isinstance(page_texts, dict) else "1")

        # Display text preview
        with st.expander("Text Preview", expanded=False):
            st.text_area("Document Text", full_text[:5000] + ("..." if len(full_text) > 5000 else ""), height=300)

# Chunking Analysis Tab
with tab2:
    if 'full_text' not in st.session_state:
        st.info("Please upload a document in the 'Document Upload' tab first.")
    else:
        st.header("Chunking Analysis")

        # Button to run chunking analysis
        if st.button("Run Chunking Analysis"):
            with st.spinner("Analyzing document with different chunking strategies..."):
                # Create selected chunking strategies
                strategies = []

                if use_fixed_size:
                    strategies.append(FixedSizeChunker(chunk_size=500, chunk_overlap=50))
                if use_sentence_based:
                    strategies.append(SentenceChunker(sentences_per_chunk=3))
                if use_recursive:
                    strategies.append(RecursiveChunker(chunk_size=500, chunk_overlap=50))
                if use_rolling_window:
                    strategies.append(RollingWindowChunker(window_size=3, step_size=1))
                if use_hybrid:
                    strategies.append(HybridSectionChunker())
                if use_production:
                    strategies.append(ProductionChunker(sentences_per_chunk=3, min_chunk_char_length=50))
                if use_production_v2:
                    strategies.append(ProductionV2Chunker(sentences_per_chunk=3, min_chunk_char_length=50))
                if use_legal_recursive:
                    strategies.append(LegalRecursiveSemanticChunker(
                        chunk_size=500,
                        chunk_overlap=100,
                        semantic_similarity_threshold=0.7,
                        min_chunk_char_length=50,
                        rolling_window_size=1
                    ))
                if use_llama_index:
                    strategies.append(LlamaIndexSemanticChunker(
                        buffer_size=1,
                        breakpoint_percentile_threshold=95,
                        min_chunk_size=3,
                        max_chunk_size=15
                    ))
                if use_semantic_double_merging:
                    strategies.append(SemanticDoubleMergingChunker(
                        initial_threshold=0.8,
                        appending_threshold=0.8,
                        merging_threshold=0.8,
                        max_chunk_size=500
                    ))

                # Process the document with each strategy
                results = {}
                for strategy in strategies:
                    start_time = time.time()
                    chunks = strategy.create_chunks(st.session_state.full_text)
                    processing_time = time.time() - start_time

                    # Analyze chunks
                    total_chunks = len(chunks)
                    token_counts = [chunk.get("token_count", count_tokens(chunk["text"])) for chunk in chunks]
                    avg_token_count = sum(token_counts) / total_chunks if total_chunks > 0 else 0
                    min_tokens = min(token_counts) if token_counts else 0
                    max_tokens = max(token_counts) if token_counts else 0

                    results[strategy.name] = {
                        "chunks": chunks,
                        "total_chunks": total_chunks,
                        "avg_token_count": avg_token_count,
                        "min_tokens": min_tokens,
                        "max_tokens": max_tokens,
                        "processing_time": processing_time
                    }

                # Store results in session state
                st.session_state.chunking_results = results

            st.success("Chunking analysis completed!")

        # Display results if available
        if 'chunking_results' in st.session_state:
            results = st.session_state.chunking_results

            # Create comparison table
            comparison_data = []
            for strategy_name, data in results.items():
                comparison_data.append({
                    "Strategy": strategy_name,
                    "Total Chunks": data["total_chunks"],
                    "Avg. Tokens": round(data["avg_token_count"], 1),
                    "Min Tokens": data["min_tokens"],
                    "Max Tokens": data["max_tokens"],
                    "Processing Time (s)": round(data["processing_time"], 3)
                })

            comparison_df = pd.DataFrame(comparison_data)
            st.subheader("Strategy Comparison")
            st.dataframe(comparison_df, use_container_width=True)

            # Display chunks for each strategy using tabs
            st.subheader("All Chunks")

            # Create tabs for each chunking strategy
            strategy_names = list(results.keys())
            chunk_tabs = st.tabs(strategy_names)

            # Display chunks for each strategy in its own tab
            for i, strategy_name in enumerate(strategy_names):
                data = results[strategy_name]
                with chunk_tabs[i]:
                    # Display strategy summary
                    st.markdown(f"""
                    ### {strategy_name} Strategy Summary

                    - **Total Chunks**: {data['total_chunks']}
                    - **Average Token Count**: {round(data['avg_token_count'], 1)}
                    - **Token Range**: {data['min_tokens']} to {data['max_tokens']}
                    - **Processing Time**: {round(data['processing_time'], 3)} seconds
                    """)

                    st.markdown("---")

                    # Add a filter for chunk search
                    search_term = st.text_input(
                        "Search within chunks:",
                        key=f"search_{strategy_name}",
                        placeholder="Enter text to filter chunks..."
                    )

                    # Add pagination controls
                    col1, col2, col3 = st.columns([1, 2, 1])
                    with col1:
                        chunks_per_page = st.selectbox(
                            "Chunks per page:",
                            options=[10, 20, 50, 100, "All"],
                            index=1,
                            key=f"per_page_{strategy_name}"
                        )

                    with col3:
                        # Calculate total pages
                        if chunks_per_page == "All":
                            total_pages = 1
                        else:
                            total_pages = max(1, (data["total_chunks"] + chunks_per_page - 1) // chunks_per_page)

                        # Page selector
                        if total_pages > 1:
                            page = st.number_input(
                                f"Page (1-{total_pages}):",
                                min_value=1,
                                max_value=total_pages,
                                value=1,
                                key=f"page_{strategy_name}"
                            )
                        else:
                            page = 1

                    # Filter chunks based on search term
                    filtered_chunks = data["chunks"]
                    if search_term:
                        filtered_chunks = [
                            chunk for chunk in data["chunks"]
                            if search_term.lower() in chunk["text"].lower()
                        ]
                        st.info(f"Found {len(filtered_chunks)} chunks containing '{search_term}'")

                    # Determine which chunks to display based on pagination
                    if chunks_per_page == "All":
                        display_chunks = filtered_chunks
                    else:
                        start_idx = (page - 1) * chunks_per_page
                        end_idx = min(start_idx + chunks_per_page, len(filtered_chunks))
                        display_chunks = filtered_chunks[start_idx:end_idx]

                    # Display chunk information and add download buttons
                    col1, col2 = st.columns([3, 1])
                    with col1:
                        st.markdown(f"Showing {len(display_chunks)} of {len(filtered_chunks)} chunks")

                    with col2:
                        # Create JSON for download
                        json_data = json.dumps({
                            "strategy": strategy_name,
                            "total_chunks": data["total_chunks"],
                            "chunks": data["chunks"]
                        }, indent=2)

                        # Create text version for download
                        text_data = f"# Chunks for {strategy_name} strategy\n\n"
                        for i, chunk in enumerate(data["chunks"]):
                            text_data += f"## Chunk {i+1}\n"
                            if "section_type" in chunk:
                                text_data += f"Section Type: {chunk['section_type']}\n"
                            if "cluster_id" in chunk:
                                text_data += f"Cluster ID: {chunk['cluster_id']}\n"
                            if "sentence_count" in chunk:
                                text_data += f"Sentences: {chunk['sentence_count']}\n"
                            text_data += f"Token Count: {chunk.get('token_count', count_tokens(chunk['text']))}\n\n"
                            text_data += f"{chunk['text']}\n\n"
                            text_data += "-" * 80 + "\n\n"

                        # Add download buttons
                        col1, col2 = st.columns(2)
                        with col1:
                            st.download_button(
                                label="Download JSON",
                                data=json_data,
                                file_name=f"{strategy_name}_chunks.json",
                                mime="application/json",
                                key=f"download_json_{strategy_name}"
                            )

                        with col2:
                            st.download_button(
                                label="Download Text",
                                data=text_data,
                                file_name=f"{strategy_name}_chunks.txt",
                                mime="text/plain",
                                key=f"download_text_{strategy_name}"
                            )

                    # Create a scrollable container for the chunks
                    with st.container(border=True, height=500):
                        for j, chunk in enumerate(display_chunks):
                            chunk_idx = filtered_chunks.index(chunk) if search_term else (
                                (page - 1) * chunks_per_page + j if chunks_per_page != "All" else j
                            )

                            # Display chunk with metadata using Streamlit native components instead of HTML
                            token_count = chunk.get("token_count", count_tokens(chunk["text"]))

                            # Create a container for metadata with columns
                            meta_cols = st.columns([1.5, 1, 1, 1])

                            # Display chunk number and token count in first column
                            meta_cols[0].markdown(f"**Chunk {chunk_idx + 1}** ({token_count} tokens)")

                            # Display additional metadata in other columns
                            if "section_type" in chunk:
                                meta_cols[1].markdown(f"**Section:** {chunk['section_type']}")

                            if "cluster_id" in chunk:
                                meta_cols[2].markdown(f"**Cluster:** {chunk['cluster_id']}")

                            if "sentence_count" in chunk:
                                meta_cols[2].markdown(f"**Sentences:** {chunk['sentence_count']}")

                            if "element_type" in chunk:
                                meta_cols[3].markdown(f"**Legal Element:** {chunk['element_type']}")

                            # Display semantic breakpoint info for LlamaIndex chunker
                            if "start_sentence_idx" in chunk and "end_sentence_idx" in chunk:
                                sentence_range = f"{chunk['start_sentence_idx']}-{chunk['end_sentence_idx']}"
                                meta_cols[3].markdown(f"**Sentence Range:** {sentence_range}")

                                # If it's a LlamaIndex semantic chunk, show the breakpoint info
                                if strategy_name.startswith("llamaindex_semantic"):
                                    meta_cols[3].markdown("**Semantic Breakpoint** 📊")

                            # Display legal elements for ProductionV2Chunker
                            if "legal_elements" in chunk:
                                legal_elements = ", ".join(chunk["legal_elements"])
                                meta_cols[3].markdown(f"**Legal Elements:** {legal_elements}")

                                # Add visual indicators for legal elements using emojis
                                icons = []
                                if "definition" in chunk["legal_elements"]:
                                    icons.append("📚 Definition")
                                if "reference" in chunk["legal_elements"]:
                                    icons.append("🔗 Reference")
                                if "numbered_clause" in chunk["legal_elements"]:
                                    icons.append("📝 Clause")
                                if "legal_term" in chunk["legal_elements"]:
                                    icons.append("⚖️ Legal Term")

                                if icons:
                                    meta_cols[0].markdown("**Contains:**")
                                    for icon in icons:
                                        meta_cols[1].markdown(f"- {icon}")

                            # Display the chunk text
                            st.text_area(
                                f"chunk_{chunk_idx}_{strategy_name}",
                                chunk["text"],
                                height=150,
                                key=f"{strategy_name}_chunk_{chunk_idx}"
                            )

                            # Add a separator between chunks
                            if j < len(display_chunks) - 1:
                                st.markdown("---")

# Retrieval Simulation Tab
with tab3:
    if 'chunking_results' not in st.session_state:
        st.info("Please run chunking analysis in the 'Chunking Analysis' tab first.")
    else:
        st.header("Retrieval Simulation")

        # Query input
        query_examples = [
            "What is the definition of the term?",
            "Who has jurisdiction in case of dispute?",
            "What are the governing laws for this agreement?",
            "What are the payment terms?",
            "What is the termination clause?",
            "What obligations survive termination?"
        ]
        selected_example = st.selectbox("Query examples (or type your own):",
                                        options=["Custom"] + query_examples,
                                        index=0)

        if selected_example == "Custom":
            user_query = st.text_input("Enter your query:", "")
        else:
            user_query = st.text_input("Enter your query:", selected_example)

        # Additional options for legal document retrieval
        with st.expander("Legal Document Retrieval Options"):
            prioritize_definitions = st.checkbox("Prioritize definitions", value=True,
                                               help="Give higher weight to chunks containing legal definitions")
            include_context = st.checkbox("Include surrounding context", value=True,
                                         help="Include chunks before and after the matched chunk for better context")

            st.info("These options are particularly effective with the Legal Recursive-Semantic Chunking strategy.")

        # Button to run retrieval simulation
        if st.button("Run Retrieval Simulation"):
            with st.spinner("Running retrieval simulation..."):
                # Get chunking results
                results = st.session_state.chunking_results

                # Initialize retrieval results
                retrieval_results = {}

                # Run retrieval for each strategy
                for strategy_name, data in results.items():
                    chunks = data["chunks"]
                    chunk_texts = [chunk["text"] for chunk in chunks]

                    # Create enhanced retrieval object
                    retriever = EnhancedRetrieval(
                        chunks=chunks,
                        embedding_model=embedding_model
                    )

                    # Run search based on selected method
                    if search_method == "BM25":
                        search_results = retriever.bm25_search(user_query, top_k=top_k)
                    elif search_method == "Semantic":
                        search_results = retriever.semantic_search(user_query, top_k=top_k)
                    else:  # Hybrid
                        # If using legal options, adjust the search parameters
                        if strategy_name == "legal_recursive_semantic_500tokens" and (prioritize_definitions or include_context):
                            # Add weights to definition chunks if prioritizing definitions
                            weights = None
                            if prioritize_definitions:
                                weights = []
                                for chunk in chunks:
                                    # Add higher weight for chunks with definitions
                                    if chunk.get("section_type") == "definition" or \
                                       chunk.get("element_type") == "definitions" or \
                                       "means" in chunk["text"].lower():
                                        weights.append(1.5)  # Higher weight for definitions
                                    else:
                                        weights.append(1.0)  # Normal weight

                            search_results = retriever.hybrid_search(
                                user_query,
                                top_k=top_k,
                                alpha=alpha,
                                weights=weights
                            )
                        else:
                            search_results = retriever.hybrid_search(user_query, top_k=top_k, alpha=alpha)

                    retrieval_results[strategy_name] = search_results

                # Store retrieval results in session state
                st.session_state.retrieval_results = retrieval_results

            st.success("Retrieval simulation completed!")

        # Display retrieval results if available
        if 'retrieval_results' in st.session_state:
            retrieval_results = st.session_state.retrieval_results

            st.subheader(f"Retrieval Results for: '{user_query}'")
            st.markdown(f"Search method: **{search_method}**" + (f" (alpha={alpha})" if search_method == "Hybrid" else ""))

            # Create tabs for each strategy
            strategy_tabs = st.tabs(list(retrieval_results.keys()))

            for i, (strategy_name, results) in enumerate(retrieval_results.items()):
                with strategy_tabs[i]:
                    for j, result in enumerate(results):
                        with st.container(border=True):
                            # Create title and metadata layout
                            st.markdown(f"**Result {j+1}** (Score: {result['score']:.4f})")

                            # Use columns for metadata display using native Streamlit components
                            has_metadata = "element_type" in result or "section_type" in result

                            if has_metadata:
                                meta_cols = st.columns([1.5, 1.5, 2])

                                # Display element type if available
                                if "element_type" in result:
                                    element_type = result["element_type"]
                                    meta_cols[0].markdown(f"**Legal element:** {element_type}")

                                # Display section type if available
                                if "section_type" in result:
                                    section_type = result["section_type"]
                                    meta_cols[1].markdown(f"**Section type:** {section_type}")

                                # Add a visual indicator for definitions
                                if "definition" in result.get("section_type", "").lower() or "definitions" in result.get("element_type", "").lower():
                                    meta_cols[2].markdown("🔍 **Contains definition**")

                                # Display legal elements for ProductionV2Chunker
                                if "legal_elements" in result and result["legal_elements"]:
                                    # Create columns specifically for legal elements
                                    legal_cols = st.columns(4)
                                    legal_cols[0].markdown("**Legal elements detected:**")

                                    # Display appropriate icons based on legal elements
                                    icons = []
                                    if "definition" in result["legal_elements"]:
                                        icons.append("📚 Definition")
                                    if "reference" in result["legal_elements"]:
                                        icons.append("🔗 Reference")
                                    if "numbered_clause" in result["legal_elements"]:
                                        icons.append("📝 Clause")
                                    if "legal_term" in result["legal_elements"]:
                                        icons.append("⚖️ Legal Term")

                                    # Display icons in columns
                                    for i, icon in enumerate(icons):
                                        legal_cols[i % 3 + 1].markdown(icon)

                            # Always display chunk text, regardless of metadata
                            st.text_area(f"result_{j}_{strategy_name}", result['text'], height=150, key=f"result_{strategy_name}_{j}")

                            # Display context from rolling window if available
                            if "context_before" in result or "context_after" in result:
                                with st.expander("Show surrounding context"):
                                    if "context_before" in result:
                                        st.markdown("**Context before:**")
                                        st.info(result["context_before"])

                                    if "context_after" in result:
                                        st.markdown("**Context after:**")
                                        st.info(result["context_after"])

                            # Add context button for legal documents if needed
                            if strategy_name == "legal_recursive_semantic_500tokens" and include_context:
                                if st.button(f"Show surrounding context", key=f"context_{strategy_name}_{j}"):
                                    st.info("Showing surrounding context from the document...")
                                    # This would ideally fetch context from the original document
                                    # For now, we'll just display a message
                                    st.text("Context would be displayed here in a real implementation.")

# Rerank Simulation Tab
with tab4:
    if 'chunking_results' not in st.session_state:
        st.info("Please run chunking analysis in the 'Chunking Analysis' tab first.")
    else:
        st.header("Rerank Simulation")

        # Query input (similar to retrieval tab)
        query_examples = [
            "What is the definition of the term?",
            "Who has jurisdiction in case of dispute?",
            "What are the governing laws for this agreement?",
            "What are the payment terms?",
            "What is the termination clause?",
            "What obligations survive termination?"
        ]
        selected_example = st.selectbox("Query examples (or type your own):",
                                        options=["Custom"] + query_examples,
                                        index=0,
                                        key="rerank_example")

        if selected_example == "Custom":
            user_query = st.text_input("Enter your query:", "", key="rerank_query")
        else:
            user_query = st.text_input("Enter your query:", selected_example, key="rerank_query_preset")

        # Reranking options
        st.subheader("Reranking Options")

        col1, col2 = st.columns(2)
        with col1:
            rerank_method = st.radio(
                "Reranking Method",
                ["Cross-Encoder Reranking", "LLM-based Reranking"],
                index=0,
                help="Cross-Encoder uses a local model for faster reranking. LLM-based uses more sophisticated analysis but is slower."
            )

            if rerank_method == "Cross-Encoder Reranking" and not has_cross_encoder:
                st.warning("CrossEncoder not available. Please install with: pip install sentence-transformers")

        with col2:
            initial_retrieval = st.radio(
                "Initial Retrieval Method",
                ["BM25", "Semantic", "Hybrid"],
                index=2,
                help="Method used for initial retrieval before reranking"
            )

            # Number of results to retrieve initially (before reranking)
            initial_top_k = st.slider("Initial retrieval pool size",
                                     min_value=10,
                                     max_value=50,
                                     value=30,
                                     help="Number of initial results to retrieve before reranking")

            # Number of results to show after reranking
            final_top_k = st.slider("Final results to show",
                                   min_value=3,
                                   max_value=20,
                                   value=10,
                                   help="Number of final results to show after reranking")

        # Button to run reranking simulation
        if st.button("Run Rerank Simulation"):
            if rerank_method == "LLM-based Reranking":
                st.warning("LLM-based reranking is currently a simulation only. In a production environment, this would call the Databricks LLM API.")

            if not user_query:
                st.error("Please enter a query first")
            else:
                with st.spinner("Running rerank simulation..."):
                    # Get chunking results
                    results = st.session_state.chunking_results

                    # Initialize reranking results
                    reranking_results = {}

                    # Run retrieval and reranking for each strategy
                    for strategy_name, data in results.items():
                        chunks = data["chunks"]

                        # Create enhanced retrieval object
                        retriever = EnhancedRetrieval(
                            chunks=chunks,
                            embedding_model=embedding_model
                        )

                        # Step 1: Get initial results based on selected method
                        if initial_retrieval == "BM25":
                            initial_results = retriever.bm25_search(user_query, top_k=initial_top_k)
                        elif initial_retrieval == "Semantic":
                            initial_results = retriever.semantic_search(user_query, top_k=initial_top_k)
                        else:  # Hybrid
                            initial_results = retriever.hybrid_search(user_query, top_k=initial_top_k, alpha=0.5)

                        # Step 2: Rerank results
                        if rerank_method == "Cross-Encoder Reranking" and reranker_model:
                            # Prepare pairs for cross-encoder
                            pairs = []
                            for result in initial_results:
                                pairs.append([user_query, result["text"]])

                            # Get scores from cross-encoder
                            try:
                                scores = reranker_model.predict(pairs)

                                # Create reranked results
                                reranked_results = []
                                for i, (result, score) in enumerate(zip(initial_results, scores)):
                                    reranked_result = result.copy()
                                    reranked_result["original_score"] = result["score"]
                                    reranked_result["score"] = float(score)
                                    reranked_result["position_change"] = i - sorted(range(len(scores)),
                                                                                   key=lambda x: scores[x],
                                                                                   reverse=True).index(i)
                                    reranked_results.append(reranked_result)

                                # Sort by new scores
                                reranked_results.sort(key=lambda x: x["score"], reverse=True)

                                # Keep only top k
                                reranked_results = reranked_results[:final_top_k]

                            except Exception as e:
                                st.error(f"Error during cross-encoder reranking: {e}")
                                reranked_results = initial_results[:final_top_k]

                        else:
                            # Simulated LLM-based reranking (in real implementation, would call LLM API)
                            # For simulation, we'll just randomly adjust scores a bit to show the concept
                            import random

                            reranked_results = []
                            for i, result in enumerate(initial_results[:initial_top_k]):
                                reranked_result = result.copy()
                                reranked_result["original_score"] = result["score"]

                                # Simulate LLM score - would be replaced with actual LLM ranking
                                # Apply some randomness but keep general order relatively similar
                                random_factor = 0.3 # How much randomness to introduce
                                position_weight = 1.0 - (i / len(initial_results[:initial_top_k]))
                                simulated_score = position_weight * (1 - random_factor) + random.random() * random_factor

                                reranked_result["score"] = float(simulated_score)
                                reranked_results.append(reranked_result)

                            # Sort by new scores
                            reranked_results.sort(key=lambda x: x["score"], reverse=True)

                            # Add position change
                            for i, result in enumerate(reranked_results):
                                original_position = initial_results.index(result)
                                result["position_change"] = original_position - i

                            # Keep only top k
                            reranked_results = reranked_results[:final_top_k]

                        # Store results for this strategy
                        reranking_results[strategy_name] = {
                            "initial_results": initial_results[:final_top_k],  # For comparison
                            "reranked_results": reranked_results
                        }

                    # Store reranking results in session state
                    st.session_state.reranking_results = reranking_results

                st.success("Reranking simulation completed!")

        # Display reranking results if available
        if 'reranking_results' in st.session_state:
            reranking_results = st.session_state.reranking_results

            st.subheader(f"Reranking Results for: '{user_query}'")
            st.markdown(f"Initial retrieval: **{initial_retrieval}** → Reranking: **{rerank_method}**")

            # Create tabs for each strategy
            strategy_tabs = st.tabs(list(reranking_results.keys()))

            for i, (strategy_name, results_data) in enumerate(reranking_results.items()):
                with strategy_tabs[i]:
                    # Allow toggling between before/after reranking
                    show_comparison = st.checkbox(f"Show before/after comparison", value=False, key=f"compare_{strategy_name}")

                    if show_comparison:
                        # Create two columns to show before and after
                        col1, col2 = st.columns(2)

                        # Before reranking
                        with col1:
                            st.markdown("### Before Reranking")
                            for j, result in enumerate(results_data["initial_results"]):
                                with st.container(border=True):
                                    st.markdown(f"**Result {j+1}** (Score: {result['score']:.4f})")

                                    # Display metadata if available (similar to retrieval tab)
                                    if "section_type" in result or "element_type" in result:
                                        meta_cols = st.columns(2)
                                        if "section_type" in result:
                                            meta_cols[0].markdown(f"**Section type:** {result['section_type']}")
                                        if "element_type" in result:
                                            meta_cols[1].markdown(f"**Element type:** {result['element_type']}")

                                    # Display text
                                    st.text_area(f"before_{j}_{strategy_name}", result['text'], height=100, key=f"before_{strategy_name}_{j}")

                        # After reranking
                        with col2:
                            st.markdown("### After Reranking")
                            for j, result in enumerate(results_data["reranked_results"]):
                                with st.container(border=True):
                                    position_change = result.get("position_change", 0)
                                    change_indicator = ""
                                    if position_change > 0:
                                        change_indicator = f"⬆️ +{position_change}"
                                    elif position_change < 0:
                                        change_indicator = f"⬇️ {position_change}"

                                    st.markdown(f"**Result {j+1}** (Score: {result['score']:.4f}) {change_indicator}")

                                    # Display metadata if available
                                    if "section_type" in result or "element_type" in result:
                                        meta_cols = st.columns(2)
                                        if "section_type" in result:
                                            meta_cols[0].markdown(f"**Section type:** {result['section_type']}")
                                        if "element_type" in result:
                                            meta_cols[1].markdown(f"**Element type:** {result['element_type']}")

                                    # Display text
                                    st.text_area(f"after_{j}_{strategy_name}", result['text'], height=100, key=f"after_{strategy_name}_{j}")
                    else:
                        # Just show reranked results
                        st.markdown("### Reranked Results")
                        for j, result in enumerate(results_data["reranked_results"]):
                            with st.container(border=True):
                                # Create title with position change indicator
                                position_change = result.get("position_change", 0)
                                change_indicator = ""
                                if position_change > 0:
                                    change_indicator = f"⬆️ +{position_change}"
                                elif position_change < 0:
                                    change_indicator = f"⬇️ {position_change}"

                                st.markdown(f"**Result {j+1}** (Score: {result['score']:.4f}) {change_indicator}")

                                # Use columns for metadata display using native Streamlit components
                                has_metadata = "element_type" in result or "section_type" in result

                                if has_metadata:
                                    meta_cols = st.columns([1.5, 1.5, 2])

                                    # Display element type if available
                                    if "element_type" in result:
                                        element_type = result["element_type"]
                                        meta_cols[0].markdown(f"**Legal element:** {element_type}")

                                    # Display section type if available
                                    if "section_type" in result:
                                        section_type = result["section_type"]
                                        meta_cols[1].markdown(f"**Section type:** {section_type}")

                                    # Add a visual indicator for definitions
                                    if "definition" in result.get("section_type", "").lower() or "definitions" in result.get("element_type", "").lower():
                                        meta_cols[2].markdown("🔍 **Contains definition**")

                                    # Display legal elements for ProductionV2Chunker
                                    if "legal_elements" in result and result["legal_elements"]:
                                        # Create columns specifically for legal elements
                                        legal_cols = st.columns(4)
                                        legal_cols[0].markdown("**Legal elements detected:**")

                                        # Display appropriate icons based on legal elements
                                        icons = []
                                        if "definition" in result["legal_elements"]:
                                            icons.append("📚 Definition")
                                        if "reference" in result["legal_elements"]:
                                            icons.append("🔗 Reference")
                                        if "numbered_clause" in result["legal_elements"]:
                                            icons.append("📝 Clause")
                                        if "legal_term" in result["legal_elements"]:
                                            icons.append("⚖️ Legal Term")

                                        # Display icons in columns
                                        for i, icon in enumerate(icons):
                                            legal_cols[i % 3 + 1].markdown(icon)

                                # Always display chunk text
                                st.text_area(f"reranked_{j}_{strategy_name}", result['text'], height=150, key=f"reranked_{strategy_name}_{j}")

                                # Display context from rolling window if available
                                if "context_before" in result or "context_after" in result:
                                    with st.expander("Show surrounding context"):
                                        if "context_before" in result:
                                            st.markdown("**Context before:**")
                                            st.info(result["context_before"])

                                        if "context_after" in result:
                                            st.markdown("**Context after:**")
                                            st.info(result["context_after"])

if __name__ == "__main__":
    # This will be executed when the script is run directly
    pass
