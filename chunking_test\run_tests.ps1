# PowerShell script to set up and run the chunking tests

# Ensure we're in the right directory
$script_dir = Split-Path -Parent $MyInvocation.MyCommand.Definition
Set-Location $script_dir

Write-Host "Setting up test environment..."

# Create virtual environment (if venv module is available)
Write-Host "Creating virtual environment..."
Try {
    python -m venv .venv
    if ($?) {
        Write-Host "Virtual environment created."
        
        # Activate virtual environment
        Write-Host "Activating virtual environment..."
        if (Test-Path ".venv\Scripts\Activate.ps1") {
            & ".venv\Scripts\Activate.ps1"
        } else {
            Write-Host "Activation script not found. Virtual environment may not have been created correctly."
            exit 1
        }
    } else {
        Write-Host "Failed to create virtual environment. Continuing without it."
    }
} Catch {
    Write-Host "Error creating virtual environment: $_"
    Write-Host "Continuing without virtual environment..."
}

# Install dependencies
Write-Host "Installing dependencies..."
python -m pip install -r requirements.txt

# Install additional visualization dependencies
Write-Host "Installing matplotlib for visualizations..."
python -m pip install matplotlib

# Install Spacy model
Write-Host "Downloading spaCy model..."
python -m spacy download en_core_web_sm

# Download NLTK data
Write-Host "Downloading NLTK data..."
python -c "import nltk; nltk.download('punkt')"

# Check if test PDF exists, if not, remind user to add it
$pdf_exists = Get-ChildItem "test_file" -Filter *.pdf
if (-not $pdf_exists) {
    Write-Host ""
    Write-Host "NOTE: No PDF file found in the test_file directory."
    Write-Host "The system will use the text file for testing, but for a complete test, you should convert"
    Write-Host "sample_legal_document.txt to PDF and place it in the test_file directory."
    Write-Host ""
}

# Run the tests
Write-Host "Running chunking tests..."
Write-Host "================================="
python chunking_test.py

# Run the definition test
Write-Host ""
Write-Host "Running definition chunking analysis..."
Write-Host "================================="
python test_challenge.py

# Run integration test
Write-Host ""
Write-Host "Running integration test..."
Write-Host "================================="
python integrate_with_retrieval.py

# Run enhanced retrieval evaluation
Write-Host ""
Write-Host "Running enhanced retrieval evaluation..."
Write-Host "================================="
python enhanced_retrieval.py

# Run semantic evaluation
Write-Host ""
Write-Host "Running semantic evaluation..."
Write-Host "================================="
python semantic_evaluation.py

Write-Host ""
Write-Host "All tests completed. Results are in the 'results' directory." 