# Databricks Reranker Model Test Script

This is a simple test script to send query-document pairs to the Databricks reranker model and display the reranking scores in the terminal.

## Prerequisites

- Python 3.8 or higher
- pandas
- requests
- numpy
- A valid Databricks API token

## Installation

1. Make sure you have the required packages installed:

```bash
pip install pandas requests numpy
```

## Usage

You can run the test in one of the following ways:

### Option 1: Run the Python script directly

1. Run the Python script:
   ```
   python databricks_reranker.py
   ```

2. When prompted, enter your Databricks API token.

### Option 2: Use the provided batch script (Windows)

1. Double-click the `run_reranker_test.bat` file or run it from the command prompt.
2. When prompted, enter your Databricks API token.

### Option 3: Use the provided PowerShell script (Windows)

1. Right-click the `run_reranker_test.ps1` file and select "Run with PowerShell" or run it from PowerShell.
2. When prompted, enter your Databricks API token.

## What the Script Does

1. Connects to the Databricks reranker endpoint using the provided token
2. Sends several query-document pairs to the reranker model:
   - Pairs with varying degrees of relevance (highly relevant, somewhat relevant, irrelevant)
   - Multiple query topics to test different contexts
3. Displays the reranking scores for each pair
4. Analyzes the results to verify that:
   - The model assigns higher scores to more relevant documents
   - The model correctly ranks documents in order of relevance
   - All scores are positive and within expected ranges

## Troubleshooting

- If you get an error about the DATABRICKS_TOKEN not being found, make sure you've set the environment variable correctly.
- If you get an authentication error, verify that your Databricks token is valid.
- If the API returns an error, check the error message for details on what went wrong.
