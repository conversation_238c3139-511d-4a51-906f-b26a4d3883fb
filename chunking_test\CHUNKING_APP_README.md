# Chunking Strategy Evaluation App

This Streamlit app allows you to evaluate different chunking strategies for RAG (Retrieval Augmented Generation) applications. You can upload documents, apply different chunking strategies, and compare retrieval results.

## Features

- Upload PDF or text documents
- Apply and compare multiple chunking strategies:
  - Fixed-size chunking (token-based)
  - Sentence-based chunking
  - Recursive chunking
  - Rolling window chunking
  - Hybrid section chunking
  - Production chunking (from your main application)
- Analyze chunk statistics (count, size, etc.)
- Simulate retrieval using:
  - BM25 search (lexical matching)
  - Semantic search (using Databricks embedding model)
  - Hybrid search (combining BM25 and semantic)
- Compare retrieval results across different chunking strategies

## Getting Started

1. Place your test documents in the `test_file` directory (optional)
2. Run the app:
   ```
   run_chunking_app.bat
   ```
   Or directly with Streamlit:
   ```
   streamlit run chunking_app.py
   ```
3. Open your browser to the URL shown in the console (usually http://localhost:8501)

## Using the App

### 1. Document Upload Tab

- Upload a PDF or text file
- View document statistics and a preview of the text

### 2. Chunking Analysis Tab

- Select which chunking strategies to use in the sidebar
- Click "Run Chunking Analysis" to process the document
- View comparison metrics and sample chunks from each strategy

### 3. Retrieval Simulation Tab

- Enter a query to test retrieval
- Select search method (BM25, Semantic, or Hybrid) in the sidebar
- Adjust alpha parameter for hybrid search if needed
- Click "Run Retrieval Simulation" to see results
- Compare retrieval results across different chunking strategies

## Chunking Strategies Explained

1. **Fixed-size chunking**: Creates chunks of a fixed token length with overlap
2. **Sentence-based chunking**: Groups a configurable number of sentences together
3. **Recursive chunking**: Splits text recursively on different separators (paragraphs, sentences, etc.)
4. **Rolling window chunking**: Creates overlapping chunks using a sliding window over sentences
5. **Hybrid section chunking**: Applies different chunking strategies to different document sections
6. **Production chunking**: Uses the same chunking strategy as your main application

## Search Methods

1. **BM25**: Lexical search based on keyword frequency (similar to TF-IDF)
2. **Semantic**: Uses the Databricks embedding model to find semantically similar chunks
3. **Hybrid**: Combines BM25 and semantic search with configurable weighting

## Tips for Evaluation

- Try different types of queries (definitions, factual questions, etc.)
- Compare how different chunking strategies handle short, important phrases
- Adjust the hybrid search alpha parameter to find the optimal balance
- Look at both the relevance scores and the actual content of retrieved chunks

## Requirements

The app uses the same dependencies as your main application, including:
- Streamlit
- PyMuPDF
- spaCy
- rank-bm25
- scikit-learn
- Databricks embedding model (from your main project)
