# Setup Instructions for Chunking Strategy Evaluation App

Follow these steps to set up and run the Chunking Strategy Evaluation App.

## 1. Create a secrets.toml file

The app uses the Databricks embedding model which requires an API token. Create a `chunking_test.streamlit/secrets.toml` file with your Databricks token:

```
# Create this directory structure
mkdir "chunking_test.streamlit"

# Create the secrets.toml file with your Databricks token
```

Add the following content to the `chunking_test.streamlit/secrets.toml` file:

```toml
DATABRICKS_TOKEN = "your_databricks_token_here"
```

Replace `your_databricks_token_here` with your actual Databricks API token.

**Note**: Streamlit looks for the secrets file in `chunking_test.streamlit/secrets.toml` (without a directory separator) rather than `chunking_test/.streamlit/secrets.toml`.

## 2. Install Required Dependencies

Make sure you have all the required dependencies installed:

```bash
pip install streamlit pymupdf spacy nltk sentence-transformers scikit-learn tiktoken torch tqdm matplotlib rank-bm25 openai
python -m spacy download en_core_web_sm
```

## 3. Run the App

Run the app using the provided batch file:

```bash
cd chunking_test
run_chunking_app.bat
```

Or directly with Streamlit:

```bash
cd chunking_test
streamlit run chunking_app.py
```

## 4. Using the Sample Document

A sample legal document is included in the `test_file` directory:

- `sample_legal_document.txt`: A simple legal agreement with definitions, articles, and sections

You can use this sample document to test the app without uploading your own files.

## 5. Fallback to SentenceTransformer

If the Databricks embedding model fails to load (e.g., if the API token is not available), the app will automatically fall back to using the SentenceTransformer model (`all-MiniLM-L6-v2`). This allows you to test the app even without a Databricks token, although the results may differ from your production environment.

## 6. Troubleshooting

If you encounter any issues:

1. Check that the `.streamlit/secrets.toml` file exists and contains a valid Databricks token
2. Ensure all dependencies are installed correctly
3. Check the console output for any error messages
4. Make sure the `embedding_model.py` file is in the chunking_test directory

## 7. Directory Structure

The app uses the following directory structure:

- `chunking_test/`: Main directory
  - `chunking_app.py`: The Streamlit app
  - `embedding_model.py`: Databricks embedding model wrapper
  - `enhanced_retrieval.py`: Enhanced retrieval functionality
  - `chunking_test.py`: Core chunking strategies
  - `.streamlit/`: Streamlit configuration
    - `secrets.toml`: API tokens and secrets
  - `test_file/`: Sample documents
    - `sample_legal_document.txt`: Sample legal agreement
  - `uploads/`: Uploaded documents
  - `results/`: Analysis results
