"""
Simple script to test the Databricks embedding model.
This script sends text to the Databricks GTE-large embedding model and prints the embeddings.
"""

import os
import sys
import numpy as np
from openai import OpenAI

# Databricks endpoint configuration
DATABRICKS_BASE_URL = "https://adb-4577621816456971.11.azuredatabricks.net/serving-endpoints"
DATABRICKS_MODEL_NAME = "databricks-gte-large-en"
EMBEDDING_DIMENSION = 1024  # GTE-large has 1024 dimensions

def get_embedding(client, text):
    """
    Get embedding for a single text string.
    
    Args:
        client: OpenAI client configured for Databricks
        text: Text to embed
        
    Returns:
        Embedding vector as a list of floats
    """
    try:
        response = client.embeddings.create(
            input=[text],
            model=DATABRICKS_MODEL_NAME
        )
        
        # Extract embedding from response
        embedding = response.data[0].embedding
        return embedding
    except Exception as e:
        print(f"Error getting embedding: {str(e)}")
        return [0.0] * EMBEDDING_DIMENSION

def calculate_similarity(embedding1, embedding2):
    """
    Calculate cosine similarity between two embeddings.
    
    Args:
        embedding1: First embedding vector
        embedding2: Second embedding vector
        
    Returns:
        Cosine similarity score (0-1)
    """
    # Convert to numpy arrays
    vec1 = np.array(embedding1)
    vec2 = np.array(embedding2)
    
    # Calculate cosine similarity
    dot_product = np.dot(vec1, vec2)
    norm1 = np.linalg.norm(vec1)
    norm2 = np.linalg.norm(vec2)
    
    # Avoid division by zero
    if norm1 == 0 or norm2 == 0:
        return 0.0
        
    return dot_product / (norm1 * norm2)

def main():
    # Get Databricks token from environment variable
    databricks_token = os.environ.get("DATABRICKS_TOKEN")
    
    if not databricks_token:
        print("Error: DATABRICKS_TOKEN environment variable not found.")
        print("Please set the DATABRICKS_TOKEN environment variable before running this script.")
        print("Example: export DATABRICKS_TOKEN=your_token_here (Linux/Mac)")
        print("Example: set DATABRICKS_TOKEN=your_token_here (Windows CMD)")
        print("Example: $env:DATABRICKS_TOKEN='your_token_here' (Windows PowerShell)")
        sys.exit(1)
    
    try:
        # Create OpenAI client with Databricks configuration
        client = OpenAI(
            api_key=databricks_token,
            base_url=DATABRICKS_BASE_URL
        )
        
        print("Testing Databricks embedding model...")
        
        # Test texts
        text1 = "The quick brown fox jumps over the lazy dog."
        text2 = "A fast auburn fox leaps above the sleepy canine."  # Similar meaning
        text3 = "Machine learning models require large amounts of training data."  # Different meaning
        
        # Get embeddings
        print(f"\nGenerating embedding for: '{text1}'")
        embedding1 = get_embedding(client, text1)
        
        print(f"Generating embedding for: '{text2}'")
        embedding2 = get_embedding(client, text2)
        
        print(f"Generating embedding for: '{text3}'")
        embedding3 = get_embedding(client, text3)
        
        # Calculate similarities
        similarity_similar = calculate_similarity(embedding1, embedding2)
        similarity_different = calculate_similarity(embedding1, embedding3)
        
        # Print results
        print("\nEmbedding Results:")
        print("-" * 50)
        print(f"Embedding dimension: {len(embedding1)}")
        print(f"First few values of embedding 1: {embedding1[:5]}...")
        
        print("\nSimilarity Analysis:")
        print("-" * 50)
        print(f"Similarity between similar sentences: {similarity_similar:.4f}")
        print(f"Similarity between different sentences: {similarity_different:.4f}")
        
        if similarity_similar > similarity_different:
            print("\nTest PASSED: Similar sentences have higher similarity score than different sentences.")
        else:
            print("\nTest FAILED: Similar sentences do not have higher similarity score than different sentences.")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
