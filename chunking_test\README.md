# Chunking Test Framework for Legal Documents

This framework implements and evaluates multiple chunking strategies for RAG (Retrieval Augmented Generation) applications, with a special focus on legal documents.

## Purpose

The primary goal is to address the challenge of effectively chunking legal documents where short, factual sentences like definitions need to be properly preserved as standalone chunks or with appropriate context.

## Features

- Tests 6 different chunking strategies
- Evaluates how well each strategy preserves important legal definitions and phrases
- Generates comparative metrics to help select the best strategy
- Special handling for legal document sections (definitions, articles, etc.)
- Advanced retrieval evaluation with multiple search methods (BM25, semantic, hybrid)
- Interactive search interface to test different chunking strategies
- Visualization of performance metrics
- Semantic evaluation against ground truth answers

## Chunking Strategies Implemented

1. **Fixed-size chunking (token-based)**: Creates chunks of a fixed token length with overlap
2. **Sentence-based chunking**: Groups a configurable number of sentences together
3. **Recursive chunking**: Splits text recursively on different separators (paragraphs, sentences, etc.)
4. **Semantic chunking**: Groups sentences by semantic similarity using embeddings
5. **Hybrid approach**: Applies different chunking strategies to different document sections
6. **Rolling window strategy**: Creates overlapping chunks using a sliding window over sentences

## Setup

1. Place your legal document PDF file in the `test_file` directory
2. Install dependencies:

```bash
pip install pymupdf spacy nltk sentence-transformers scikit-learn tiktoken torch tqdm matplotlib rank-bm25
python -m spacy download en_core_web_sm
```

Or simply run one of the setup scripts:

```bash
# On Windows
.\run_tests.ps1

# On Unix/Linux/Mac
./run_tests.sh
```

## Usage

Run the test suite:

```bash
# On Windows
.\run_tests.ps1

# On Unix/Linux/Mac
./run_tests.sh
```

Or run individual modules:

```bash
# Basic chunking tests
python chunking_test.py

# Definition challenge test
python test_challenge.py

# Integration with retrieval
python integrate_with_retrieval.py

# Enhanced retrieval evaluation
python enhanced_retrieval.py

# Semantic evaluation against ground truth
python semantic_evaluation.py
```

The scripts will:
1. Load the first PDF file found in the `test_file` directory
2. Extract and process the text
3. Apply all chunking strategies
4. Save the results to the `results` directory
5. Evaluate how well each strategy preserves key legal phrases
6. Compare retrieval performance across strategies
7. Evaluate semantic similarity to ground truth answers

## Enhanced Retrieval Evaluation

The `enhanced_retrieval.py` module provides advanced functionality for evaluating and comparing different chunking strategies:

### Multiple Search Methods
- **BM25 search**: Lexical matching based on keyword frequency
- **Semantic search**: Using embeddings for semantic similarity
- **Hybrid search**: Combining BM25 and semantic search for better results

### Comprehensive Evaluation
- Evaluates retrieval performance across multiple test queries
- Measures execution time for each strategy
- Analyzes chunk statistics (size, count, etc.)
- Visualizes results with charts and graphs

### Interactive Search
- Interactive command-line interface to test queries
- Compare results across all chunking strategies
- Choose between BM25, semantic, or hybrid search methods
- See real-time performance metrics

## Semantic Evaluation

The `semantic_evaluation.py` module provides a ground-truth based approach to evaluate chunking strategies:

### Ground Truth Evaluation
- Tests against predefined legal queries with known answers
- Measures semantic similarity between retrieved chunks and correct answers
- Provides objective comparison of chunking strategies

### Semantic Similarity Metrics
- Best match similarity: How close the best retrieved chunk is to the ground truth
- Average similarity: Average semantic relevance of all retrieved chunks
- Cross-strategy comparison: Ranks strategies by their ability to preserve meaning

### Visualization
- Bar charts showing semantic performance across strategies
- Detailed similarity scores for each query and strategy
- Sortable results to quickly identify the best strategy

## Result Analysis

The framework evaluates chunking strategies in four main ways:

1. **Chunk statistics**: Number of chunks, average token count, and token range
2. **Key phrase detection**: How well each strategy preserves critical legal terms and definitions
3. **Retrieval performance**: How effectively chunks can be retrieved using different search methods
4. **Semantic similarity**: How close retrieved chunks are to ground truth answers

Results are saved in the `results` directory:
- `test_[strategy].json`: Contains all chunks created by each strategy
- `phrase_eval_[strategy].json`: Analysis of how well each strategy preserves key phrases
- `bm25_search_[query].json`: Results of BM25 search for specific queries
- `enhanced_retrieval_[method].json`: Comprehensive evaluation results
- `enhanced_retrieval_[method].png`: Visualization of evaluation metrics
- `semantic_evaluation_[method].json`: Ground truth evaluation results
- `semantic_evaluation_[method].png`: Visualization of semantic similarity metrics

## Recommended Strategy for Legal Documents

For legal documents, the hybrid approach is usually most effective:
- Single-sentence chunks for definition sections
- 2-3 sentence chunks for articles and operational text
- Larger chunks for narrative or background sections

The rolling window strategy also works well for ensuring short, important phrases are included in multiple contexts.

## Customization

You can customize the key phrases to search for by modifying the `key_phrases` list in the `main()` function of `chunking_test.py`.

For legal documents, consider including phrases like:
- Defined terms (e.g., "Dollars", "Agreement")
- Common legal definitions ("shall mean", "is defined as")
- Currency symbols ("$", "€")
- Important clause indicators ("governing law", "termination") 

You can also customize test queries in `enhanced_retrieval.py` and ground truth data in `semantic_evaluation.py` to focus on specific aspects of the document. 