"""
Simple script to test the Databricks Llama model by sending a "hello" message.
This script sends a message to the Databricks Meta Llama 3 model and prints the response.
"""

import os
import sys
from openai import OpenAI

# Databricks endpoint configuration
DATABRICKS_BASE_URL = "https://adb-4577621816456971.11.azuredatabricks.net/serving-endpoints"
DATABRICKS_LLM_MODEL = "databricks-meta-llama-3-3-70b-instruct"

def main():
    # Get Databricks token from environment variable
    databricks_token = os.environ.get("DATABRICKS_TOKEN")
    
    if not databricks_token:
        print("Error: DATABRICKS_TOKEN environment variable not found.")
        print("Please set the DATABRICKS_TOKEN environment variable before running this script.")
        print("Example: export DATABRICKS_TOKEN=your_token_here (Linux/Mac)")
        print("Example: set DATABRICKS_TOKEN=your_token_here (Windows CMD)")
        print("Example: $env:DATABRI<PERSON>KS_TOKEN='your_token_here' (Windows PowerShell)")
        sys.exit(1)
    
    try:
        # Create OpenAI client with Databricks configuration
        client = OpenAI(
            api_key=databricks_token,
            base_url=DATABRICKS_BASE_URL
        )
        
        print("Sending 'hello' message to Databricks Llama model...")
        
        # Create a simple message
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello! How are you today?"}
        ]
        
        # Make the API call
        response = client.chat.completions.create(
            messages=messages,
            model=DATABRICKS_LLM_MODEL,
            max_tokens=100
        )
        
        # Extract and print the response
        if response.choices and len(response.choices) > 0:
            print("\nResponse from Databricks Llama model:")
            print("-" * 50)
            print(response.choices[0].message.content)
            print("-" * 50)
        else:
            print("Received empty response from Databricks LLM API")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
