"""
Databricks embedding model integration for chunking test app.

This module provides a wrapper around the Databricks embedding API to make it
compatible with the SentenceTransformer interface used in the application.
"""

import numpy as np
import streamlit as st
from openai import OpenAI
from typing import List, Union
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Databricks endpoint URL
DATABRICKS_BASE_URL = "https://adb-4577621816456971.11.azuredatabricks.net/serving-endpoints"
DATABRICKS_MODEL_NAME = "databricks-gte-large-en"

@st.cache_resource
def get_databricks_client():
    """
    Creates and caches an OpenAI client configured for Databricks.

    Returns:
        OpenAI client or None if token is not available
    """
    try:
        # For testing without secrets.toml, use a dummy token
        # This allows the app to run in fallback mode with SentenceTransformer
        databricks_token = "dummy_token_for_testing"

        # Try to get token from Streamlit secrets if available
        try:
            databricks_token = st.secrets["DATABRICKS_TOKEN"]
        except:
            logger.warning("DATABRICKS_TOKEN not found in secrets.toml, using fallback embedding model")
            return None

        # Create OpenAI client with Databricks configuration
        client = OpenAI(
            api_key=databricks_token,
            base_url=DATABRICKS_BASE_URL
        )
        logger.info("Databricks OpenAI client created successfully")
        return client
    except Exception as e:
        logger.error(f"Error creating Databricks client: {e}")
        # Don't show error to user, just log it and return None to use fallback
        return None


class DatabricksEmbeddingModel:
    """
    Wrapper class for Databricks embedding model to provide a similar interface
    to SentenceTransformer for easy integration with existing code.
    """

    def __init__(self):
        self.client = get_databricks_client()
        self.model_name = DATABRICKS_MODEL_NAME
        self.embedding_dimension = 1024  # GTE-large has 1024 dimensions

    def encode(self,
               sentences: Union[str, List[str]],
               convert_to_tensor: bool = True,
               # Additional parameters for compatibility with SentenceTransformer
               show_progress_bar: bool = False,
               batch_size: int = 100) -> Union[List[List[float]], np.ndarray]:
        """
        Generate embeddings for the given sentences using Databricks API.

        Args:
            sentences: String or list of strings to encode
            convert_to_tensor: If True, returns a numpy array (ignored if client is None)
            show_progress_bar: Ignored, kept for compatibility with SentenceTransformer
            batch_size: Size of batches to process, default 100

        Returns:
            List of embeddings or numpy array if convert_to_tensor=True
        """
        if self.client is None:
            logger.error("Cannot encode text: Databricks client not initialized")
            # Return zero embeddings as fallback
            if isinstance(sentences, str):
                return np.zeros(self.embedding_dimension) if convert_to_tensor else [0.0] * self.embedding_dimension
            else:
                empty_embeddings = [np.zeros(self.embedding_dimension) for _ in range(len(sentences))]
                return np.array(empty_embeddings) if convert_to_tensor else [[0.0] * self.embedding_dimension for _ in range(len(sentences))]

        try:
            # Handle single string vs list of strings
            is_single_sentence = isinstance(sentences, str)
            input_texts = [sentences] if is_single_sentence else sentences

            # Process in batches if needed (OpenAI API may have limits)
            # Using the batch_size parameter passed to the function
            all_embeddings = []

            # Ignore show_progress_bar parameter to avoid IDE warning
            _ = show_progress_bar

            for i in range(0, len(input_texts), batch_size):
                batch = input_texts[i:i+batch_size]
                response = self.client.embeddings.create(
                    input=batch,
                    model=self.model_name
                )

                # Extract embeddings from response
                batch_embeddings = [item.embedding for item in response.data]
                all_embeddings.extend(batch_embeddings)

            # Return appropriate format
            if is_single_sentence:
                result = all_embeddings[0]
                return np.array(result) if convert_to_tensor else result
            else:
                return np.array(all_embeddings) if convert_to_tensor else all_embeddings

        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            # Return zero embeddings as fallback
            if isinstance(sentences, str):
                return np.zeros(self.embedding_dimension) if convert_to_tensor else [0.0] * self.embedding_dimension
            else:
                empty_embeddings = [np.zeros(self.embedding_dimension) for _ in range(len(sentences))]
                return np.array(empty_embeddings) if convert_to_tensor else [[0.0] * self.embedding_dimension for _ in range(len(sentences))]


@st.cache_resource
def load_databricks_embedding_model():
    """
    Loads and caches the Databricks embedding model.

    This function is cached using Streamlit's cache_resource decorator to avoid
    recreating the model on each Streamlit rerun, which is a performance optimization.

    Returns:
        DatabricksEmbeddingModel instance or SentenceTransformer as fallback
    """
    try:
        logger.info(f"Loading Databricks embedding model: {DATABRICKS_MODEL_NAME}")
        model = DatabricksEmbeddingModel()

        # Test the model with a simple input to verify API connection
        test_embedding = model.encode("Test sentence", convert_to_tensor=True)
        if isinstance(test_embedding, np.ndarray) and test_embedding.shape[0] > 0:
            logger.info(f"Databricks embedding model loaded successfully. Embedding dimension: {test_embedding.shape[0]}")
            return model
        else:
            logger.warning("Databricks embedding model test failed: Invalid embedding format")
            # Fall back to SentenceTransformer
            return load_sentence_transformer_fallback()
    except Exception as e:
        logger.warning(f"Error loading Databricks embedding model: {e}")
        # Fall back to SentenceTransformer
        return load_sentence_transformer_fallback()

def load_sentence_transformer_fallback():
    """
    Loads a SentenceTransformer model as a fallback when Databricks model fails.

    Returns:
        SentenceTransformer instance or None if initialization fails
    """
    try:
        from sentence_transformers import SentenceTransformer
        logger.info("Loading SentenceTransformer as fallback")
        model = SentenceTransformer("all-MiniLM-L6-v2")
        logger.info("SentenceTransformer loaded successfully")
        return model
    except Exception as e:
        logger.error(f"Error loading SentenceTransformer fallback: {e}")
        return None
