"""
Enhanced Retrieval Evaluation for Chunking Strategies

This module provides advanced functionality for evaluating and comparing
different chunking strategies using multiple retrieval methods:
1. BM25 search (lexical matching)
2. Semantic search (using embeddings)
3. Hybrid search (combining BM25 and semantic)

It also includes evaluation metrics and visualization tools to help
understand how different chunking strategies affect retrieval performance.
"""

import os
import sys
import json
import time
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Any, Tuple, Optional, Union
from collections import defaultdict

# Add parent directory to path to import from main project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import from our chunking test module
try:
    from chunking_test.chunking_test import (
        extract_text_from_pdf,
        ChunkingStrategy,
        SentenceChunker,
        HybridSectionChunker,
        RollingWindowChunker,
        ProductionChunker,
        FixedSizeChunker,
        RecursiveChunker,
        SemanticChunker,
        perform_bm25_search,
        count_tokens,
        RESULTS_DIR
    )
except ImportError:
    # Try alternative import path
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    try:
        from chunking_test import (
            extract_text_from_pdf,
            ChunkingStrategy,
            SentenceChunker,
            HybridSectionChunker,
            RollingWindowChunker,
            ProductionChunker,
            FixedSizeChunker,
            RecursiveChunker,
            SemanticChunker,
            perform_bm25_search,
            count_tokens,
            RESULTS_DIR
        )
    except ImportError:
        print("Error: Could not import chunking strategies. Make sure chunking_test.py is in the current directory.")
        sys.exit(1)

# Check for required libraries
try:
    from rank_bm25 import BM25Okapi
    has_bm25 = True
except ImportError:
    has_bm25 = False
    print("Warning: rank_bm25 not installed. BM25 search will be disabled.")

try:
    from sentence_transformers import SentenceTransformer, util
    import torch
    has_transformers = True
except ImportError:
    has_transformers = False
    print("Warning: sentence-transformers not installed. Semantic search will be disabled.")

# Load sentence embeddings model if available
embedding_model = None
if has_transformers:
    try:
        # First try to import our local Databricks embedding model
        try:
            from embedding_model import load_databricks_embedding_model
            embedding_model = load_databricks_embedding_model()
            if embedding_model is not None:
                print("Loaded Databricks embedding model for enhanced retrieval evaluation")
            else:
                # Fall back to SentenceTransformer if Databricks model fails
                embedding_model = SentenceTransformer("all-MiniLM-L6-v2")
                print("Loaded SentenceTransformer embedding model for enhanced retrieval evaluation")
        except ImportError:
            # Fall back to SentenceTransformer
            embedding_model = SentenceTransformer("all-MiniLM-L6-v2")
            print("Loaded SentenceTransformer embedding model for enhanced retrieval evaluation (Databricks model not found)")
    except Exception as e:
        print(f"Error loading embedding model: {e}")


class EnhancedRetrieval:
    """
    Enhanced retrieval class that combines multiple search methods
    and provides detailed evaluation metrics.
    """

    def __init__(self, chunks=None, embedding_model=None):
        """
        Initialize with an optional embedding model for semantic search
        and optional chunks for pre-computing embeddings
        """
        self.embedding_model = embedding_model
        self.chunks = chunks
        self.chunk_embeddings = None

        # If no embedding model provided, try to load default
        if has_transformers and self.embedding_model is None:
            try:
                self.embedding_model = SentenceTransformer("all-MiniLM-L6-v2")
            except Exception as e:
                print(f"Error loading default embedding model: {e}")

        # Pre-compute embeddings if chunks are provided
        if self.chunks and self.embedding_model:
            self.precompute_embeddings()

    def precompute_embeddings(self):
        """Pre-compute embeddings for all chunks to speed up searches"""
        if not self.chunks or not self.embedding_model:
            return

        chunk_texts = [chunk.get("text", "") for chunk in self.chunks]
        try:
            print(f"Pre-computing embeddings for {len(chunk_texts)} chunks...")
            self.chunk_embeddings = self.embedding_model.encode(chunk_texts, convert_to_tensor=True)
            print("Embeddings pre-computed successfully")
        except Exception as e:
            print(f"Error pre-computing embeddings: {e}")
            self.chunk_embeddings = None

    def semantic_search(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Performs semantic search on the given chunks using the query.
        Returns a list of dictionaries containing chunk text and score.

        This version is optimized for the Streamlit app interface.
        """
        if not self.embedding_model:
            print("Semantic search is not available. No embedding model provided.")
            return []

        if not self.chunks:
            print("No chunks to search.")
            return []

        try:
            # Encode query
            query_embedding = self.embedding_model.encode(query, convert_to_tensor=True)

            # Use pre-computed embeddings if available, otherwise compute on the fly
            if self.chunk_embeddings is not None:
                chunk_embeddings = self.chunk_embeddings
            else:
                chunk_texts = [chunk.get("text", "") for chunk in self.chunks]
                chunk_embeddings = self.embedding_model.encode(chunk_texts, convert_to_tensor=True)

            # Calculate cosine similarity
            if hasattr(util, 'cos_sim'):
                # For sentence-transformers
                cos_scores = util.cos_sim(query_embedding, chunk_embeddings)[0]
                scores = cos_scores.cpu().numpy()
            else:
                # For Databricks embedding model (numpy arrays)
                import numpy as np
                from sklearn.metrics.pairwise import cosine_similarity

                # Convert to numpy if needed
                if hasattr(query_embedding, 'cpu'):
                    query_embedding = query_embedding.cpu().numpy()
                if hasattr(chunk_embeddings, 'cpu'):
                    chunk_embeddings = chunk_embeddings.cpu().numpy()

                # Reshape for sklearn
                query_embedding = query_embedding.reshape(1, -1)
                scores = cosine_similarity(query_embedding, chunk_embeddings)[0]

            # Create result list with scores
            results = []
            for i, (chunk, score) in enumerate(zip(self.chunks, scores)):
                results.append({
                    'text': chunk.get("text", ""),
                    'score': float(score),
                    'chunk_id': chunk.get("chunk_id", f"chunk_{i}"),
                    'token_count': chunk.get("token_count", 0)
                })

            # Sort by score (descending)
            results.sort(key=lambda x: x['score'], reverse=True)

            # Return top k results
            return results[:top_k]

        except Exception as e:
            print(f"Error in semantic search: {e}")
            return []

    def bm25_search(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Performs BM25 search on the chunks.
        Returns a list of dictionaries containing chunk text and score.

        This version is optimized for the Streamlit app interface.
        """
        if not has_bm25:
            print("BM25 search is not available. Please install rank_bm25.")
            return []

        if not self.chunks:
            print("No chunks to search.")
            return []

        try:
            # Extract chunk texts and tokenize
            chunk_texts = [chunk.get("text", "") for chunk in self.chunks]
            tokenized_chunks = [text.lower().split() for text in chunk_texts]

            # Create BM25 model
            bm25 = BM25Okapi(tokenized_chunks)

            # Tokenize query
            tokenized_query = query.lower().split()

            # Get scores
            scores = bm25.get_scores(tokenized_query)

            # Create result list with scores
            results = []
            for i, (chunk, score) in enumerate(zip(self.chunks, scores)):
                results.append({
                    'text': chunk.get("text", ""),
                    'score': float(score),
                    'chunk_id': chunk.get("chunk_id", f"chunk_{i}"),
                    'token_count': chunk.get("token_count", 0)
                })

            # Sort by score (descending)
            results.sort(key=lambda x: x['score'], reverse=True)

            # Return top k results
            return results[:top_k]

        except Exception as e:
            print(f"Error in BM25 search: {e}")
            return []

    def hybrid_search(self, query: str, top_k: int = 5, alpha: float = 0.5) -> List[Dict[str, Any]]:
        """
        Performs hybrid search (combining BM25 and semantic) on the chunks.
        Uses weighted average of normalized BM25 and semantic scores.
        Returns a list of dictionaries containing chunk text and score.

        This version is optimized for the Streamlit app interface.

        Args:
            query: The search query
            top_k: Number of top results to return
            alpha: Weight for BM25 score (0-1), where 1 means BM25 only
        """
        if not has_bm25:
            print("BM25 search is not available. Falling back to semantic search only.")
            return self.semantic_search(query, top_k)

        if not self.embedding_model:
            print("Semantic search is not available. Falling back to BM25 search only.")
            return self.bm25_search(query, top_k)

        if not self.chunks:
            print("No chunks to search.")
            return []

        try:
            # Get all results from both methods
            bm25_results = self.bm25_search(query, len(self.chunks))
            semantic_results = self.semantic_search(query, len(self.chunks))

            # Create dictionaries to map chunk_id to score
            bm25_scores = {result['chunk_id']: result['score'] for result in bm25_results}
            semantic_scores = {result['chunk_id']: result['score'] for result in semantic_results}

            # Get all unique chunk IDs
            all_chunk_ids = set(bm25_scores.keys()).union(set(semantic_scores.keys()))

            # Normalize scores (min-max normalization)
            bm25_max = max(bm25_scores.values()) if bm25_scores else 1.0
            bm25_min = min(bm25_scores.values()) if bm25_scores else 0.0
            semantic_max = max(semantic_scores.values()) if semantic_scores else 1.0
            semantic_min = min(semantic_scores.values()) if semantic_scores else 0.0

            # Combine scores using weighted average
            combined_results = []
            for i, chunk in enumerate(self.chunks):
                chunk_id = chunk.get("chunk_id", f"chunk_{i}")

                # Get scores, defaulting to min if not found
                bm25_score = bm25_scores.get(chunk_id, bm25_min)
                semantic_score = semantic_scores.get(chunk_id, semantic_min)

                # Normalize scores
                if bm25_max > bm25_min:
                    bm25_normalized = (bm25_score - bm25_min) / (bm25_max - bm25_min)
                else:
                    bm25_normalized = 0.0

                if semantic_max > semantic_min:
                    semantic_normalized = (semantic_score - semantic_min) / (semantic_max - semantic_min)
                else:
                    semantic_normalized = 0.0

                # Calculate weighted average
                combined_score = (alpha * bm25_normalized) + ((1 - alpha) * semantic_normalized)

                combined_results.append({
                    'text': chunk.get("text", ""),
                    'score': float(combined_score),
                    'chunk_id': chunk_id,
                    'token_count': chunk.get("token_count", 0),
                    'bm25_score': float(bm25_normalized),
                    'semantic_score': float(semantic_normalized)
                })

            # Sort by combined score (descending)
            combined_results.sort(key=lambda x: x['score'], reverse=True)

            # Return top k results
            return combined_results[:top_k]

        except Exception as e:
            print(f"Error in hybrid search: {e}")
            return []

    def perform_semantic_search(self, chunks: List[Dict[str, Any]], query: str, top_k: int = 5) -> List[Tuple[Dict[str, Any], float]]:
        """
        Legacy method for compatibility with existing code.
        Performs semantic search on the given chunks using the query.
        Returns a list of tuples containing (chunk, score).
        """
        if not self.embedding_model:
            print("Semantic search is not available. No embedding model provided.")
            return []

        if not chunks:
            print("No chunks to search.")
            return []

        # Extract chunk texts
        chunk_texts = [chunk.get("text", "") for chunk in chunks]

        try:
            # Encode chunks and query
            query_embedding = self.embedding_model.encode(query, convert_to_tensor=True)
            chunk_embeddings = self.embedding_model.encode(chunk_texts, convert_to_tensor=True)

            # Calculate cosine similarity
            if hasattr(util, 'cos_sim'):
                # For sentence-transformers
                cos_scores = util.cos_sim(query_embedding, chunk_embeddings)[0]
                scores = cos_scores.cpu().numpy()
            else:
                # For Databricks embedding model (numpy arrays)
                import numpy as np
                from sklearn.metrics.pairwise import cosine_similarity

                # Convert to numpy if needed
                if hasattr(query_embedding, 'cpu'):
                    query_embedding = query_embedding.cpu().numpy()
                if hasattr(chunk_embeddings, 'cpu'):
                    chunk_embeddings = chunk_embeddings.cpu().numpy()

                # Reshape for sklearn
                query_embedding = query_embedding.reshape(1, -1)
                scores = cosine_similarity(query_embedding, chunk_embeddings)[0]

            # Pair chunks with scores and sort by score (descending)
            chunk_scores = list(zip(chunks, scores))
            chunk_scores.sort(key=lambda x: x[1], reverse=True)

            # Return top k results
            return chunk_scores[:top_k]
        except Exception as e:
            print(f"Error in semantic search: {e}")
            return []

    def perform_hybrid_search(self, chunks: List[Dict[str, Any]], query: str,
                              top_k: int = 5, bm25_weight: float = 0.5) -> List[Tuple[Dict[str, Any], float]]:
        """
        Legacy method for compatibility with existing code.
        Performs hybrid search (combining BM25 and semantic) on the given chunks.
        Uses weighted average of normalized BM25 and semantic scores.
        Returns a list of tuples containing (chunk, score).
        """
        # Store the original chunks
        original_chunks = self.chunks

        # Temporarily set the chunks to the provided ones
        self.chunks = chunks

        try:
            # Use the new hybrid_search method
            results = self.hybrid_search(query, top_k, bm25_weight)

            # Convert to the legacy format (list of tuples)
            legacy_results = []
            for result in results:
                # Find the original chunk
                for chunk in chunks:
                    if chunk.get("chunk_id", "") == result["chunk_id"]:
                        legacy_results.append((chunk, result["score"]))
                        break

            return legacy_results
        finally:
            # Restore the original chunks
            self.chunks = original_chunks

    def evaluate_retrieval(self, strategies: List[ChunkingStrategy], full_text: str,
                           queries: List[str], retrieval_method: str = "hybrid",
                           top_k: int = 5) -> Dict[str, Any]:
        """
        Evaluates retrieval performance of multiple chunking strategies across multiple queries.

        Args:
            strategies: List of chunking strategies to evaluate
            full_text: The document text to chunk
            queries: List of queries to test
            retrieval_method: One of "bm25", "semantic", or "hybrid"
            top_k: Number of top results to consider

        Returns:
            Dictionary with evaluation results
        """
        results = {
            "method": retrieval_method,
            "top_k": top_k,
            "queries": queries,
            "strategies": {},
            "timing": {},
            "chunk_stats": {}
        }

        # Process each strategy
        for strategy in strategies:
            print(f"\nCreating chunks with {strategy.name} strategy...")
            start_time = time.time()
            chunks = strategy.create_chunks(full_text)
            chunk_time = time.time() - start_time

            # Analyze chunks
            chunk_token_counts = [chunk.get("token_count", count_tokens(chunk.get("text", ""))) for chunk in chunks]
            avg_chunk_size = sum(chunk_token_counts) / len(chunks) if chunks else 0

            # Add chunk statistics
            results["chunk_stats"][strategy.name] = {
                "num_chunks": len(chunks),
                "avg_chunk_size": avg_chunk_size,
                "min_size": min(chunk_token_counts) if chunks else 0,
                "max_size": max(chunk_token_counts) if chunks else 0,
                "creation_time": chunk_time
            }

            query_results = {}

            # Process each query
            for query in queries:
                print(f"Evaluating query: '{query}' with {retrieval_method} search...")
                start_time = time.time()

                # Run appropriate search method
                if retrieval_method == "bm25":
                    search_results = perform_bm25_search(chunks, query, top_k)
                elif retrieval_method == "semantic":
                    search_results = self.perform_semantic_search(chunks, query, top_k)
                else:  # hybrid
                    search_results = self.perform_hybrid_search(chunks, query, top_k, bm25_weight=0.5)

                search_time = time.time() - start_time

                # Extract just the necessary information
                processed_results = []
                for i, (chunk, score) in enumerate(search_results):
                    processed_results.append({
                        "position": i + 1,
                        "chunk_id": chunk.get("chunk_id", "unknown"),
                        "score": float(score),
                        "text": chunk.get("text", "")[:300] + "..." if len(chunk.get("text", "")) > 300 else chunk.get("text", ""),
                        "token_count": chunk.get("token_count", count_tokens(chunk.get("text", "")))
                    })

                query_results[query] = {
                    "results": processed_results,
                    "search_time": search_time
                }

            results["strategies"][strategy.name] = query_results
            results["timing"][strategy.name] = {
                "total_time": chunk_time + sum(qr["search_time"] for qr in query_results.values())
            }

        return results

    def save_evaluation_results(self, results: Dict[str, Any], filename_prefix: str = "enhanced_retrieval"):
        """Saves the evaluation results to a JSON file"""
        os.makedirs(RESULTS_DIR, exist_ok=True)

        method = results.get("method", "unknown")
        output_path = os.path.join(RESULTS_DIR, f"{filename_prefix}_{method}.json")

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2)

        print(f"Saved retrieval evaluation results to {output_path}")

        # Generate visualization
        self.generate_evaluation_visualization(results, output_path.replace(".json", ".png"))

        return output_path

    def generate_evaluation_visualization(self, results: Dict[str, Any], output_path: str):
        """Generates visualization of evaluation results"""
        try:
            import matplotlib.pyplot as plt
            import numpy as np

            strategies = list(results["strategies"].keys())
            queries = results["queries"]
            n_strategies = len(strategies)
            n_queries = len(queries)

            # Create figure
            plt.figure(figsize=(12, 8))

            # Plot average scores
            ax1 = plt.subplot(2, 2, 1)
            avg_scores = []
            for strategy in strategies:
                scores = []
                for query in queries:
                    query_results = results["strategies"][strategy].get(query, {}).get("results", [])
                    if query_results:
                        # Average score of top results
                        scores.append(sum(r.get("score", 0) for r in query_results) / len(query_results))
                avg_scores.append(sum(scores) / len(scores) if scores else 0)

            ax1.bar(strategies, avg_scores)
            ax1.set_title("Average Relevance Score by Strategy")
            ax1.set_xticklabels(strategies, rotation=45, ha="right")
            plt.tight_layout()

            # Plot number of chunks
            ax2 = plt.subplot(2, 2, 2)
            num_chunks = [results["chunk_stats"].get(strategy, {}).get("num_chunks", 0) for strategy in strategies]
            ax2.bar(strategies, num_chunks)
            ax2.set_title("Number of Chunks by Strategy")
            ax2.set_xticklabels(strategies, rotation=45, ha="right")
            plt.tight_layout()

            # Plot average chunk size
            ax3 = plt.subplot(2, 2, 3)
            avg_sizes = [results["chunk_stats"].get(strategy, {}).get("avg_chunk_size", 0) for strategy in strategies]
            ax3.bar(strategies, avg_sizes)
            ax3.set_title("Average Chunk Size (tokens) by Strategy")
            ax3.set_xticklabels(strategies, rotation=45, ha="right")
            plt.tight_layout()

            # Plot timing information
            ax4 = plt.subplot(2, 2, 4)
            timings = [results["timing"].get(strategy, {}).get("total_time", 0) for strategy in strategies]
            ax4.bar(strategies, timings)
            ax4.set_title("Total Processing Time (seconds) by Strategy")
            ax4.set_xticklabels(strategies, rotation=45, ha="right")
            plt.tight_layout()

            # Save the figure
            plt.savefig(output_path, dpi=300, bbox_inches="tight")
            plt.close()

            print(f"Saved visualization to {output_path}")
        except Exception as e:
            print(f"Error generating visualization: {e}")

    def run_interactive_search(self, strategies: List[ChunkingStrategy], full_text: str):
        """
        Runs an interactive search session allowing the user to input queries
        and compare results from different chunking strategies.
        """
        if not strategies:
            print("No chunking strategies provided.")
            return

        print("\n" + "="*60)
        print("INTERACTIVE SEARCH SESSION")
        print("="*60)
        print("Enter your search queries to compare chunking strategies.")
        print("Type 'exit' or 'quit' to end the session.")

        # Pre-create chunks for each strategy to speed up searching
        chunks_by_strategy = {}
        for strategy in strategies:
            print(f"Creating chunks with {strategy.name} strategy...")
            chunks_by_strategy[strategy.name] = strategy.create_chunks(full_text)
            print(f"Created {len(chunks_by_strategy[strategy.name])} chunks")

        # Main interactive loop
        while True:
            try:
                query = input("\nEnter search query: ")
                if query.lower() in ['exit', 'quit', 'q']:
                    print("Exiting interactive search session.")
                    break

                if not query.strip():
                    print("Please enter a valid query.")
                    continue

                # Ask for search method
                search_method = input("Choose search method (bm25/semantic/hybrid) [default=hybrid]: ").lower()
                if not search_method or search_method not in ['bm25', 'semantic', 'hybrid']:
                    search_method = 'hybrid'

                top_k = 3  # Default number of results to show

                print(f"\nSearching for: '{query}' using {search_method} search method...")
                print("="*60)

                # Run search for each strategy
                for strategy_name, chunks in chunks_by_strategy.items():
                    print(f"\n--- {strategy_name} ---")

                    start_time = time.time()
                    if search_method == 'bm25':
                        results = perform_bm25_search(chunks, query, top_k)
                    elif search_method == 'semantic':
                        results = self.perform_semantic_search(chunks, query, top_k)
                    else:  # hybrid
                        results = self.perform_hybrid_search(chunks, query, top_k)

                    search_time = time.time() - start_time

                    print(f"Search time: {search_time:.3f} seconds")
                    print(f"Top {len(results)} results:")

                    for i, (chunk, score) in enumerate(results):
                        print(f"\nResult {i+1} - Score: {score:.4f}")
                        chunk_text = chunk.get("text", "")

                        # Print a preview of the text (first 300 chars)
                        print(f"Text: {chunk_text[:300]}..." if len(chunk_text) > 300 else chunk_text)

            except KeyboardInterrupt:
                print("\nInteractive session interrupted.")
                break
            except Exception as e:
                print(f"Error during search: {e}")


def main():
    """Main function to demonstrate the enhanced retrieval"""
    # Check if test file exists
    script_dir = os.path.dirname(os.path.abspath(__file__))
    test_file_dir = os.path.join(script_dir, "test_file")

    # Find available test files
    pdf_files = [f for f in os.listdir(test_file_dir) if f.endswith('.pdf')]
    txt_files = [f for f in os.listdir(test_file_dir) if f.endswith('.txt')]

    if pdf_files:
        test_file = os.path.join(test_file_dir, pdf_files[0])
        print(f"Using PDF file: {test_file}")
        full_text, _ = extract_text_from_pdf(test_file)
    elif txt_files:
        test_file = os.path.join(test_file_dir, txt_files[0])
        print(f"Using text file: {test_file}")
        with open(test_file, 'r', encoding='utf-8') as f:
            full_text = f.read()
    else:
        print(f"No test files found. Please add a PDF or text file to {test_file_dir}")
        return

    # Create chunking strategies
    strategies = [
        FixedSizeChunker(chunk_size=500, chunk_overlap=50),
        SentenceChunker(sentences_per_chunk=3),
        RollingWindowChunker(window_size=3, step_size=1),
        HybridSectionChunker(),
        ProductionChunker(sentences_per_chunk=3, min_chunk_char_length=50),
    ]

    # Add semantic chunking if available
    if has_transformers and embedding_model is not None:
        strategies.append(SemanticChunker())

    # Set up enhanced retrieval
    enhanced_retrieval = EnhancedRetrieval(embedding_model)

    # Define test queries focused on definitions and short important facts
    test_queries = [
        "What is the definition of Dollars?",
        "What is the lawful currency?",
        "What does $ mean in this agreement?",
        "Who is the Borrower?",
        "Who is the Lender?",
    ]

    # Run evaluation
    print("\nEvaluating retrieval performance across all chunking strategies...")
    evaluation_results = enhanced_retrieval.evaluate_retrieval(
        strategies=strategies,
        full_text=full_text,
        queries=test_queries,
        retrieval_method="hybrid",
        top_k=3
    )

    # Save results
    enhanced_retrieval.save_evaluation_results(evaluation_results)

    # Run interactive search
    enhanced_retrieval.run_interactive_search(strategies, full_text)


if __name__ == "__main__":
    main()