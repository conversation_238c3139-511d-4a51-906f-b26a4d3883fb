# Databricks Embedding Model Test

This is a simple test script to test the Databricks GTE-large embedding model by generating embeddings for sample texts and comparing their similarities.

## Prerequisites

- Python 3.8 or higher
- OpenAI Python package
- NumPy package
- A valid Databricks API token

## Installation

1. Make sure you have the required packages installed:

```bash
pip install openai numpy
```

## Usage

### Option 1: Run the Python script directly

1. Set the DATABRICKS_TOKEN environment variable:

   **Windows CMD:**
   ```
   set DATABRICKS_TOKEN=your_token_here
   ```

   **Windows PowerShell:**
   ```
   $env:DATABRICKS_TOKEN='your_token_here'
   ```

   **Linux/Mac:**
   ```
   export DATABRICKS_TOKEN=your_token_here
   ```

2. Run the Python script:

   ```
   python databricks_embedding.py
   ```

### Option 2: Use the batch script (Windows CMD)

1. Run the batch script:

   ```
   run_embedding_test.bat
   ```

   Or provide the token as an argument:

   ```
   run_embedding_test.bat your_token_here
   ```

### Option 3: Use the PowerShell script (Windows PowerShell)

1. Run the PowerShell script:

   ```
   .\run_embedding_test.ps1
   ```

   Or provide the token as an argument:

   ```
   .\run_embedding_test.ps1 your_token_here
   ```

## What the Script Does

1. Connects to the Databricks endpoint using the provided token
2. Generates embeddings for three sample texts:
   - "The quick brown fox jumps over the lazy dog."
   - "A fast auburn fox leaps above the sleepy canine." (similar meaning)
   - "Machine learning models require large amounts of training data." (different meaning)
3. Calculates the cosine similarity between the embeddings
4. Displays the embedding dimensions, sample values, and similarity scores
5. Verifies that similar sentences have higher similarity scores than different sentences

## Expected Output

The script should show that the similarity score between the two sentences about foxes is higher than the similarity score between the fox sentence and the machine learning sentence. This demonstrates that the embedding model is capturing semantic meaning correctly.

## Troubleshooting

- If you get an error about the DATABRICKS_TOKEN not being found, make sure you've set the environment variable correctly.
- If you get an authentication error, verify that your Databricks token is valid.
- If you get an error about the OpenAI or NumPy modules not being found, make sure you've installed them with `pip install openai numpy`.

## Cleanup

These are temporary test scripts. You can delete them after testing:
- databricks_embedding.py
- run_embedding_test.bat
- run_embedding_test.ps1
- README.md
