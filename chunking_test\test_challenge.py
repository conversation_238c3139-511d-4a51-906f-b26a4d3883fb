"""
This script demonstrates the specific problem with chunking short definitions
in legal documents.

It focuses on showing how standard chunking approaches often fail to correctly
identify and retrieve important short definitions like "Dollars" or "$".
"""

import os
import sys
import json
from typing import List, Dict, Any

# Add parent directory to path to import from main project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the chunking strategies
from chunking_test import (
    extract_text_from_pdf,
    SentenceChunker,
    HybridSectionChunker,
    ProductionChunker,
    count_tokens
)

# For testing with actual text file if PDF is not available
def read_text_file(file_path: str) -> str:
    """Read text from a file if PDF is not available"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"Error reading text file: {e}")
        return ""


def find_definition_in_chunks(chunks: List[Dict[str, Any]], term: str) -> Dict[str, Any]:
    """
    Search for a specific definition term in the list of chunks
    and return information about how it was chunked.
    """
    results = {
        "term": term,
        "found_in_chunks": [],
        "chunk_sizes": [],
        "context_quality": []
    }
    
    for i, chunk in enumerate(chunks):
        chunk_text = chunk.get("text", "").lower()
        term_lower = term.lower()
        
        if term_lower in chunk_text:
            # Check if it's a proper definition (has "means" or similar nearby)
            is_definition = (
                "means" in chunk_text or 
                "shall mean" in chunk_text or 
                "defined as" in chunk_text or
                "refers to" in chunk_text
            )
            
            # Calculate how much context is provided
            token_count = chunk.get("token_count", count_tokens(chunk_text))
            
            # Analyze whether the definition is self-contained in the chunk
            context_quality = "poor"
            if is_definition and token_count < 100:
                context_quality = "excellent"  # Small chunk with definition
            elif is_definition and token_count < 300:
                context_quality = "good"       # Medium chunk with definition
            elif is_definition:
                context_quality = "fair"       # Large chunk with definition
            
            results["found_in_chunks"].append(i)
            results["chunk_sizes"].append(token_count)
            results["context_quality"].append(context_quality)
            
    return results


def analyze_definition_chunking(text: str, terms_to_check: List[str]):
    """
    Analyze how different chunking strategies handle definition terms.
    Compares standard sentence-based chunking with hybrid chunking.
    """
    if not text:
        print("No text to analyze.")
        return
    
    print("\nAnalyzing definition chunking for legal text...")
    
    # Create chunks using different strategies
    standard_chunker = SentenceChunker(sentences_per_chunk=3)  # Standard approach
    hybrid_chunker = HybridSectionChunker()  # Our improved approach
    production_chunker = ProductionChunker(sentences_per_chunk=3, min_chunk_char_length=50)  # Production strategy
    
    standard_chunks = standard_chunker.create_chunks(text)
    hybrid_chunks = hybrid_chunker.create_chunks(text)
    production_chunks = production_chunker.create_chunks(text)
    
    print(f"Created {len(standard_chunks)} standard chunks, {len(hybrid_chunks)} hybrid chunks, and {len(production_chunks)} production chunks.")
    
    # Analyze how each strategy handles definition terms
    results = {
        "standard": {},
        "hybrid": {},
        "production": {}
    }
    
    for term in terms_to_check:
        print(f"\nAnalyzing term: '{term}'")
        
        standard_results = find_definition_in_chunks(standard_chunks, term)
        hybrid_results = find_definition_in_chunks(hybrid_chunks, term)
        production_results = find_definition_in_chunks(production_chunks, term)
        
        results["standard"][term] = standard_results
        results["hybrid"][term] = hybrid_results
        results["production"][term] = production_results
        
        # Print summary for this term
        print(f"Standard chunking: Found in {len(standard_results['found_in_chunks'])} chunks")
        if standard_results["found_in_chunks"]:
            for i, chunk_idx in enumerate(standard_results["found_in_chunks"]):
                print(f"  Chunk {chunk_idx}: {standard_results['chunk_sizes'][i]} tokens, " 
                      f"context quality: {standard_results['context_quality'][i]}")
                print(f"  Text: {standard_chunks[chunk_idx]['text'][:150]}...")
        
        print(f"Hybrid chunking: Found in {len(hybrid_results['found_in_chunks'])} chunks")
        if hybrid_results["found_in_chunks"]:
            for i, chunk_idx in enumerate(hybrid_results["found_in_chunks"]):
                print(f"  Chunk {chunk_idx}: {hybrid_results['chunk_sizes'][i]} tokens, "
                      f"context quality: {hybrid_results['context_quality'][i]}")
                print(f"  Text: {hybrid_chunks[chunk_idx]['text'][:150]}...")
        
        print(f"Production chunking: Found in {len(production_results['found_in_chunks'])} chunks")
        if production_results["found_in_chunks"]:
            for i, chunk_idx in enumerate(production_results["found_in_chunks"]):
                print(f"  Chunk {chunk_idx}: {production_results['chunk_sizes'][i]} tokens, "
                      f"context quality: {production_results['context_quality'][i]}")
                print(f"  Text: {production_chunks[chunk_idx]['text'][:150]}...")
    
    # Save the detailed analysis
    output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "results")
    os.makedirs(output_dir, exist_ok=True)
    
    with open(os.path.join(output_dir, "definition_chunking_analysis.json"), "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\nSaved detailed analysis to {os.path.join(output_dir, 'definition_chunking_analysis.json')}")


def main():
    # Check for test files
    test_file_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_file")
    text_file = os.path.join(test_file_dir, "sample_legal_document.txt")
    pdf_files = [f for f in os.listdir(test_file_dir) if f.endswith('.pdf')]
    
    # Use PDF if available, otherwise use text file
    if pdf_files:
        pdf_file = os.path.join(test_file_dir, pdf_files[0])
        print(f"Using PDF file: {pdf_file}")
        text, _ = extract_text_from_pdf(pdf_file)
    elif os.path.exists(text_file):
        print(f"Using text file: {text_file}")
        text = read_text_file(text_file)
    else:
        print(f"No test files found. Please add a PDF or text file to {test_file_dir}")
        return
    
    # Test terms that are likely to be short definitions
    test_terms = [
        "Dollars",
        "$",
        "Agreement",
        "Borrower",
        "Lender",
        "GAAP"
    ]
    
    # Analyze how different chunking strategies handle these terms
    analyze_definition_chunking(text, test_terms)


if __name__ == "__main__":
    main() 