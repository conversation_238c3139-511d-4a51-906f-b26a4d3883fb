# PowerShell script to run the Databricks embedding test with the token

# Prompt for the Databricks token if not provided as an argument
if ($args.Count -eq 0) {
    $DATABRICKS_TOKEN = Read-Host -Prompt "Enter your Databricks token"
} else {
    $DATABRICKS_TOKEN = $args[0]
}

# Set the environment variable
$env:DATABRICKS_TOKEN = $DATABRICKS_TOKEN

# Run the Python script
Write-Host "Running databricks_embedding.py..."
python databricks_embedding.py

# Pause to see the output
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
