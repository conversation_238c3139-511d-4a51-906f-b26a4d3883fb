"""
Chunking Test Framework for Legal Documents

This script implements and evaluates multiple chunking strategies for RAG applications,
with special focus on legal documents.

Strategies implemented:
1. Fixed-size chunking (token-based)
2. Sentence-based chunking
3. Recursive chunking
4. Semantic chunking
5. Hybrid approach (Adaptive chunking based on document sections)
6. Rolling window strategy
"""

import os
import re
import sys
import json
import fitz  # PyMuPDF
import nltk
import torch
import spacy
from typing import List, Dict, Any, Tuple, Optional
from collections import defaultdict
from tqdm import tqdm
import numpy as np

# Ensure spaCy and NLTK models are downloaded
try:
    import en_core_web_sm
    nlp = en_core_web_sm.load()
except ImportError:
    spacy.cli.download("en_core_web_sm")
    nlp = spacy.load("en_core_web_sm")

try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

# Import transformers for semantic chunking and embeddings
try:
    from sentence_transformers import SentenceTransformer
    from sklearn.cluster import KMeans
    has_transformers = True
except ImportError:
    has_transformers = False
    print("Warning: sentence-transformers not installed. Semantic chunking will be disabled.")

# For token-based chunking
try:
    import tiktoken
    has_tiktoken = True
except ImportError:
    has_tiktoken = False
    print("Warning: tiktoken not installed. Token-based chunking will use rough estimations.")

# Add BM25 for search functionality
try:
    from rank_bm25 import BM25Okapi
    has_bm25 = True
except ImportError:
    has_bm25 = False
    print("Warning: rank_bm25 not installed. BM25 search will be disabled. Install with: pip install rank-bm25")

# Configure paths
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
TEST_FILE_DIR = os.path.join(SCRIPT_DIR, "test_file")
RESULTS_DIR = os.path.join(SCRIPT_DIR, "results")

# Ensure results directory exists
os.makedirs(RESULTS_DIR, exist_ok=True)

# Load sentence embeddings model if available
embedding_model = None
if has_transformers:
    try:
        # First try to import our local Databricks embedding model
        try:
            from embedding_model import load_databricks_embedding_model
            embedding_model = load_databricks_embedding_model()
            if embedding_model is not None:
                print("Loaded Databricks embedding model")
            else:
                # Fall back to SentenceTransformer if Databricks model fails
                embedding_model = SentenceTransformer("all-MiniLM-L6-v2")
                print("Loaded SentenceTransformer embedding model")
        except ImportError:
            # Fall back to SentenceTransformer
            embedding_model = SentenceTransformer("all-MiniLM-L6-v2")
            print("Loaded SentenceTransformer embedding model (Databricks model not found)")
    except Exception as e:
        print(f"Error loading embedding model: {e}")


# Helper functions
def count_tokens(text: str) -> int:
    """Count the number of tokens in text using tiktoken if available, otherwise estimate"""
    if has_tiktoken:
        try:
            encoder = tiktoken.get_encoding("cl100k_base")  # OpenAI's encoding
            return len(encoder.encode(text))
        except:
            pass
    # Fallback: rough estimation (whitespace-based)
    return len(text.split())


def extract_text_from_pdf(pdf_path: str) -> Tuple[str, Dict[int, str]]:
    """Extract text from PDF file and return full text and page-by-page text."""
    full_text = ""
    page_texts = {}

    try:
        doc = fitz.open(pdf_path)
        for page_num, page in enumerate(doc):
            text = page.get_text()
            if text.strip():
                page_texts[page_num] = text
                full_text += text + "\n\n"

        return full_text, page_texts
    except Exception as e:
        print(f"Error extracting text from PDF: {e}")
        return "", {}


def identify_document_sections(text: str) -> Dict[str, List[Tuple[int, int]]]:
    """
    Identify important sections in legal documents like definitions, articles, etc.
    Returns a dictionary mapping section types to a list of (start, end) indices.
    """
    sections = {
        "definitions": [],  # For definitions sections
        "articles": [],     # For articles
        "regular": []       # For regular content
    }

    # Pattern for definition sections
    definitions_pattern = re.compile(r'(?i)(definitions|defined\s+terms|interpretation)[\s\S]*?(section|article)', re.MULTILINE)

    # Find definitions sections
    for match in definitions_pattern.finditer(text):
        start, end = match.span()
        sections["definitions"].append((start, end))

    # Identify articles or numbered sections
    article_pattern = re.compile(r'(?i)(article|section)\s+(\d+|[ivxlcdm]+)[\s\S]*?(article|section|$)', re.MULTILINE)

    for match in article_pattern.finditer(text):
        start, end = match.span()

        # Check if this overlaps with definitions
        is_definition = False
        for def_start, def_end in sections["definitions"]:
            if (start >= def_start and start < def_end) or \
               (end > def_start and end <= def_end) or \
               (start <= def_start and end >= def_end):
                is_definition = True
                break

        if not is_definition:
            sections["articles"].append((start, end))

    # Everything else is regular content
    last_end = 0
    all_sections = sorted(sections["definitions"] + sections["articles"], key=lambda x: x[0])

    for start, end in all_sections:
        if start > last_end:
            sections["regular"].append((last_end, start))
        last_end = end

    if last_end < len(text):
        sections["regular"].append((last_end, len(text)))

    return sections


class ChunkingStrategy:
    """Base class for all chunking strategies"""

    def __init__(self, name: str):
        self.name = name

    def create_chunks(self, text: str, **kwargs) -> List[Dict[str, Any]]:
        """Create chunks from text and return them as a list of dictionaries"""
        raise NotImplementedError("Subclasses must implement this method")

    def save_results(self, chunks: List[Dict[str, Any]], filename: str):
        """Save chunks to a file in the results directory"""
        output_path = os.path.join(RESULTS_DIR, f"{filename}_{self.name}.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump({
                "strategy": self.name,
                "num_chunks": len(chunks),
                "chunks": chunks
            }, f, indent=2)
        print(f"Saved {len(chunks)} chunks using {self.name} strategy to {output_path}")


class FixedSizeChunker(ChunkingStrategy):
    """Simple fixed-size chunking based on token count"""

    def __init__(self, chunk_size: int = 500, chunk_overlap: int = 50):
        super().__init__(f"fixed_size_{chunk_size}tokens")
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

    def create_chunks(self, text: str, **kwargs) -> List[Dict[str, Any]]:
        chunks = []

        # Split text into tokens (or approximate)
        if has_tiktoken:
            encoder = tiktoken.get_encoding("cl100k_base")
            tokens = encoder.encode(text)

            i = 0
            chunk_id = 0

            while i < len(tokens):
                # Extract chunk_size tokens
                chunk_end = min(i + self.chunk_size, len(tokens))
                chunk_tokens = tokens[i:chunk_end]
                chunk_text = encoder.decode(chunk_tokens)

                chunks.append({
                    "chunk_id": f"chunk_{chunk_id}",
                    "text": chunk_text,
                    "token_count": len(chunk_tokens)
                })

                # Advance by chunk_size - chunk_overlap
                i += self.chunk_size - self.chunk_overlap
                chunk_id += 1

        else:
            # Fallback to character-based chunking
            chars_per_token = 4  # rough estimate
            char_length = self.chunk_size * chars_per_token
            overlap_chars = self.chunk_overlap * chars_per_token

            i = 0
            chunk_id = 0

            while i < len(text):
                # Extract chunk_size characters
                chunk_end = min(i + char_length, len(text))

                # Try to end at a sentence or paragraph break
                if chunk_end < len(text):
                    for separator in ['\n\n', '\n', '.', '!', '?', ' ']:
                        pos = text.rfind(separator, i, chunk_end)
                        if pos > i:
                            chunk_end = pos + 1
                            break

                chunk_text = text[i:chunk_end]
                token_count = count_tokens(chunk_text)

                chunks.append({
                    "chunk_id": f"chunk_{chunk_id}",
                    "text": chunk_text,
                    "token_count": token_count
                })

                # Advance with overlap
                i = i + char_length - overlap_chars
                if i >= len(text) - 20:  # avoid tiny chunks at the end
                    break
                chunk_id += 1

        return chunks


class SentenceChunker(ChunkingStrategy):
    """Chunks text based on sentences"""

    def __init__(self, sentences_per_chunk: int = 3):
        super().__init__(f"sentence_based_{sentences_per_chunk}sentences")
        self.sentences_per_chunk = sentences_per_chunk

    def create_chunks(self, text: str, **kwargs) -> List[Dict[str, Any]]:
        chunks = []

        # Process the text with spaCy
        doc = nlp(text)
        sentences = list(doc.sents)

        chunk_id = 0
        for i in range(0, len(sentences), self.sentences_per_chunk):
            # Get the next N sentences
            chunk_sentences = sentences[i:i + self.sentences_per_chunk]
            if not chunk_sentences:
                continue

            # Concatenate sentences into a chunk
            chunk_text = " ".join([s.text for s in chunk_sentences])

            if chunk_text.strip():
                chunks.append({
                    "chunk_id": f"chunk_{chunk_id}",
                    "text": chunk_text,
                    "sentence_count": len(chunk_sentences),
                    "token_count": count_tokens(chunk_text)
                })
                chunk_id += 1

        return chunks


class RecursiveChunker(ChunkingStrategy):
    """Recursive chunking that uses separators like paragraphs, sentences, etc."""

    def __init__(self, chunk_size: int = 500, chunk_overlap: int = 50):
        super().__init__(f"recursive_{chunk_size}tokens")
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

        # Separators in order of preference
        self.separators = ["\n\n", "\n", ".", ";", ":", "!", "?", ",", " ", ""]

    def create_chunks(self, text: str, **kwargs) -> List[Dict[str, Any]]:
        chunks = []

        def split_text_recursive(text, separators):
            """Recursively split text based on separators"""
            # Base case 1: Text is small enough
            if count_tokens(text) <= self.chunk_size:
                return [text]

            # Base case 2: No more separators left
            if not separators:
                return [text[:self.chunk_size]]

            # Try to split on the current separator
            separator = separators[0]
            parts = text.split(separator)

            # If we couldn't split (only one part) or the parts are too big,
            # try the next separator
            if len(parts) == 1 or max(count_tokens(p) for p in parts) > self.chunk_size:
                return split_text_recursive(text, separators[1:])

            # Process each part with the remaining separators
            chunks = []
            for part in parts:
                if count_tokens(part) > self.chunk_size:
                    chunks.extend(split_text_recursive(part, separators[1:]))
                elif part:
                    chunks.append(part)

            return chunks

        # Split the text recursively
        text_chunks = split_text_recursive(text, self.separators)

        # Now create overlapping chunks from the basic splits
        final_chunks = []
        current_chunk = ""
        current_tokens = 0

        for chunk_text in text_chunks:
            chunk_token_count = count_tokens(chunk_text)

            if current_tokens + chunk_token_count <= self.chunk_size:
                # Add to current chunk
                if current_chunk:
                    current_chunk += " " + chunk_text
                else:
                    current_chunk = chunk_text
                current_tokens += chunk_token_count
            else:
                # Save current chunk and start a new one
                if current_chunk:
                    final_chunks.append(current_chunk)

                # Start new chunk with overlap
                overlap_text = ""
                if current_chunk and self.chunk_overlap > 0:
                    # Get the tail of the previous chunk for overlap
                    words = current_chunk.split()
                    overlap_word_count = self.chunk_overlap // 4  # rough estimate
                    if len(words) > overlap_word_count:
                        overlap_text = " ".join(words[-overlap_word_count:]) + " "

                current_chunk = overlap_text + chunk_text
                current_tokens = count_tokens(current_chunk)

        # Add the last chunk if not empty
        if current_chunk:
            final_chunks.append(current_chunk)

        # Create the final chunk objects
        for i, chunk_text in enumerate(final_chunks):
            chunks.append({
                "chunk_id": f"chunk_{i}",
                "text": chunk_text,
                "token_count": count_tokens(chunk_text)
            })

        return chunks


class SemanticChunker(ChunkingStrategy):
    """Semantic chunking based on sentence embeddings and clustering"""

    def __init__(self, target_chunk_count: int = None, max_sentences_per_chunk: int = 5):
        super().__init__("semantic")
        self.target_chunk_count = target_chunk_count
        self.max_sentences_per_chunk = max_sentences_per_chunk

    def create_chunks(self, text: str, **kwargs) -> List[Dict[str, Any]]:
        if not has_transformers or embedding_model is None:
            print("Semantic chunking unavailable without sentence-transformers")
            return []

        chunks = []

        # Extract sentences
        doc = nlp(text)
        sentences = list(doc.sents)

        if not sentences:
            return []

        # Calculate number of clusters
        if self.target_chunk_count is None:
            # Estimate chunks based on total tokens
            total_tokens = count_tokens(text)
            self.target_chunk_count = max(1, total_tokens // 500)

        n_clusters = min(len(sentences), self.target_chunk_count)

        # Create sentence embeddings
        sentence_texts = [sent.text for sent in sentences]
        embeddings = embedding_model.encode(sentence_texts)

        # Cluster sentences with K-means
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        clusters = kmeans.fit_predict(embeddings)

        # Group sentences by cluster
        cluster_to_sentences = defaultdict(list)
        cluster_to_indices = defaultdict(list)

        for i, cluster_id in enumerate(clusters):
            cluster_to_sentences[cluster_id].append(sentences[i].text)
            cluster_to_indices[cluster_id].append(i)

        # Sort clusters by the minimum sentence index they contain
        # This helps maintain some of the original document ordering
        sorted_clusters = sorted(cluster_to_indices.items(),
                                key=lambda x: min(x[1]))

        # Create chunks from clusters
        for i, (cluster_id, _) in enumerate(sorted_clusters):
            cluster_sentences = cluster_to_sentences[cluster_id]

            # Break large clusters into smaller chunks
            for j in range(0, len(cluster_sentences), self.max_sentences_per_chunk):
                sub_chunk = cluster_sentences[j:j + self.max_sentences_per_chunk]
                chunk_text = " ".join(sub_chunk)

                if chunk_text.strip():
                    chunks.append({
                        "chunk_id": f"chunk_{i}_{j//self.max_sentences_per_chunk}",
                        "text": chunk_text,
                        "cluster_id": int(cluster_id),
                        "token_count": count_tokens(chunk_text)
                    })

        return chunks


class HybridSectionChunker(ChunkingStrategy):
    """
    Adaptive chunking based on document sections.
    Uses different chunking strategies for different sections.
    """

    def __init__(self):
        super().__init__("hybrid_section")

    def create_chunks(self, text: str, **kwargs) -> List[Dict[str, Any]]:
        chunks = []

        # Identify document sections
        sections = identify_document_sections(text)

        # Create chunkers for different section types
        definitions_chunker = SentenceChunker(sentences_per_chunk=1)  # 1 sentence per chunk for definitions
        articles_chunker = SentenceChunker(sentences_per_chunk=3)     # 3 sentences for articles
        regular_chunker = RecursiveChunker(chunk_size=500)           # Recursive for regular content

        chunk_id = 0

        # Process definitions (small chunks)
        for start, end in sections["definitions"]:
            section_text = text[start:end]
            section_chunks = definitions_chunker.create_chunks(section_text)

            for chunk in section_chunks:
                chunks.append({
                    "chunk_id": f"chunk_{chunk_id}",
                    "text": chunk["text"],
                    "section_type": "definitions",
                    "token_count": count_tokens(chunk["text"])
                })
                chunk_id += 1

        # Process articles (medium chunks)
        for start, end in sections["articles"]:
            section_text = text[start:end]
            section_chunks = articles_chunker.create_chunks(section_text)

            for chunk in section_chunks:
                chunks.append({
                    "chunk_id": f"chunk_{chunk_id}",
                    "text": chunk["text"],
                    "section_type": "articles",
                    "token_count": count_tokens(chunk["text"])
                })
                chunk_id += 1

        # Process regular content (larger chunks)
        for start, end in sections["regular"]:
            section_text = text[start:end]
            section_chunks = regular_chunker.create_chunks(section_text)

            for chunk in section_chunks:
                chunks.append({
                    "chunk_id": f"chunk_{chunk_id}",
                    "text": chunk["text"],
                    "section_type": "regular",
                    "token_count": count_tokens(chunk["text"])
                })
                chunk_id += 1

        return chunks


class RollingWindowChunker(ChunkingStrategy):
    """
    Rolling window strategy that creates overlapping chunks
    to ensure short, important sentences are captured in multiple contexts.
    """

    def __init__(self, window_size: int = 3, step_size: int = 1):
        super().__init__(f"rolling_window_{window_size}sent_step{step_size}")
        self.window_size = window_size
        self.step_size = step_size

    def create_chunks(self, text: str, **kwargs) -> List[Dict[str, Any]]:
        chunks = []

        # Process the text with spaCy to get sentences
        doc = nlp(text)
        sentences = list(doc.sents)

        if not sentences:
            return []

        chunk_id = 0
        for i in range(0, len(sentences) - self.window_size + 1, self.step_size):
            # Get window_size sentences starting from position i
            window_sentences = sentences[i:i + self.window_size]
            chunk_text = " ".join([s.text for s in window_sentences])

            if chunk_text.strip():
                chunks.append({
                    "chunk_id": f"chunk_{chunk_id}",
                    "text": chunk_text,
                    "window_start": i,
                    "sentence_count": len(window_sentences),
                    "token_count": count_tokens(chunk_text)
                })
                chunk_id += 1

        return chunks


class ProductionChunker(ChunkingStrategy):
    """
    Replicates the chunking strategy used in production
    (from src/keyword_code/processors/pdf_processor.py)

    Uses spaCy for sentence segmentation, groups N sentences per chunk,
    and enforces a minimum character length.
    """

    def __init__(self, sentences_per_chunk: int = 3, min_chunk_char_length: int = 50):
        super().__init__(f"production_{sentences_per_chunk}sent_min{min_chunk_char_length}chars")
        self.sentences_per_chunk = sentences_per_chunk
        self.min_chunk_char_length = min_chunk_char_length

    def create_chunks(self, text: str, **kwargs) -> List[Dict[str, Any]]:
        chunks = []

        # Process the text with spaCy
        doc = nlp(text)
        sentences = list(doc.sents)

        chunk_id = 0
        for i in range(0, len(sentences), self.sentences_per_chunk):
            # Get the next N sentences
            current_sentence_group = sentences[i:i + self.sentences_per_chunk]
            if not current_sentence_group:
                continue

            # Concatenate sentences into a chunk
            chunk_text_from_sentences = " ".join([sent.text for sent in current_sentence_group]).strip()

            # Skip if chunk is too short or empty after normalization
            # This mimics the production code's check: len(normalize_text(chunk_text_from_sentences)) < min_chunk_char_length
            # Since we don't have normalize_text here, we'll just use the raw length as an approximation
            if not chunk_text_from_sentences or len(chunk_text_from_sentences) < self.min_chunk_char_length:
                continue

            chunks.append({
                "chunk_id": f"chunk_{chunk_id}",
                "text": chunk_text_from_sentences,
                "sentence_count": len(current_sentence_group),
                "token_count": count_tokens(chunk_text_from_sentences),
                "char_length": len(chunk_text_from_sentences)
            })
            chunk_id += 1

        return chunks


class ProductionV2Chunker(ProductionChunker):
    """
    Enhanced version of the production chunker that adds legal document section identification
    and tagging to each chunk as metadata. This combines the simplicity of the production chunker
    with the legal-specific metadata from the LegalRecursiveSemanticChunker.
    """

    def __init__(self, sentences_per_chunk: int = 3, min_chunk_char_length: int = 50):
        super().__init__(sentences_per_chunk, min_chunk_char_length)
        # Override the name to reflect this is v2
        self.name = f"production_v2_{sentences_per_chunk}sent_min{min_chunk_char_length}chars"

        # Patterns to identify legal references, adapted from LegalRecursiveSemanticChunker
        self.legal_reference_patterns = [
            re.compile(r'(?i)section\s+\d+'),
            re.compile(r'(?i)article\s+\d+'),
            re.compile(r'(?i)paragraph\s+\d+'),
            re.compile(r'(?i)clause\s+\d+'),
            re.compile(r'§\s*\d+'),
            re.compile(r'(?i)see\s+[^.;:!?]+')
        ]

    def identify_legal_elements(self, chunk_text: str) -> Dict[str, bool]:
        """
        Identify legal-specific elements in a chunk text and return as metadata.

        Args:
            chunk_text: The text of the chunk to analyze

        Returns:
            Dictionary of boolean flags indicating presence of different legal elements
        """
        legal_metadata = {
            "has_definition": False,
            "has_reference": False,
            "has_numbered_clause": False,
            "has_legal_term": False
        }

        # Check for definitions
        if re.search(r'(?i)"([^"]+)"\s+(means|shall mean|is defined as|refers to)', chunk_text):
            legal_metadata["has_definition"] = True

        # Check for legal references
        for pattern in self.legal_reference_patterns:
            if pattern.search(chunk_text):
                legal_metadata["has_reference"] = True
                break

        # Check for numbered clauses
        if re.search(r'(?i)(\(\d+\)|\d+\.|\([a-z]\)|\(([ivx]+)\))\s+[A-Z]', chunk_text):
            legal_metadata["has_numbered_clause"] = True

        # Check for common legal terminology
        legal_terms = [
            r'(?i)\bhereby\b', r'(?i)\bwhereas\b', r'(?i)\bshall\b',
            r'(?i)\bpursuant to\b', r'(?i)\bin accordance with\b',
            r'(?i)\bnotwithstanding\b', r'(?i)\bhereinafter\b',
            r'(?i)\bforth?with\b', r'(?i)\bunder no circumstance\b'
        ]

        for term in legal_terms:
            if re.search(term, chunk_text):
                legal_metadata["has_legal_term"] = True
                break

        return legal_metadata

    def identify_section_type(self, chunk_text: str, doc_sections: Dict[str, List[Tuple[int, int]]], text: str) -> str:
        """
        Identify which section of the document a chunk belongs to.

        Args:
            chunk_text: The text of the chunk
            doc_sections: Document sections identified by identify_document_sections
            text: The full document text

        Returns:
            Section type as string ("definitions", "articles", "regular", or "unknown")
        """
        # Try to find the chunk in the original text
        chunk_start = text.find(chunk_text)
        if chunk_start == -1:
            return "unknown"

        chunk_end = chunk_start + len(chunk_text)

        # Check which section the chunk belongs to
        for section_type, sections in doc_sections.items():
            for start, end in sections:
                # If chunk is fully contained in a section or has significant overlap
                if (chunk_start >= start and chunk_end <= end) or \
                   (chunk_start < end and chunk_end > start and (min(chunk_end, end) - max(chunk_start, start)) > len(chunk_text) / 2):
                    return section_type

        return "unknown"

    def create_chunks(self, text: str, **kwargs) -> List[Dict[str, Any]]:
        # Get base chunks from parent implementation
        chunks = super().create_chunks(text, **kwargs)

        # Identify document sections
        doc_sections = identify_document_sections(text)

        # Enhance each chunk with legal metadata
        enhanced_chunks = []
        for chunk in chunks:
            chunk_text = chunk["text"]

            # Identify legal elements
            legal_metadata = self.identify_legal_elements(chunk_text)

            # Identify section type
            section_type = self.identify_section_type(chunk_text, doc_sections, text)

            # Add metadata to the chunk
            enhanced_chunk = chunk.copy()
            enhanced_chunk.update(legal_metadata)
            enhanced_chunk["section_type"] = section_type

            # Add a summary of legal elements for easy filtering
            legal_elements = []
            if legal_metadata["has_definition"]:
                legal_elements.append("definition")
            if legal_metadata["has_reference"]:
                legal_elements.append("reference")
            if legal_metadata["has_numbered_clause"]:
                legal_elements.append("numbered_clause")
            if legal_metadata["has_legal_term"]:
                legal_elements.append("legal_term")

            if legal_elements:
                enhanced_chunk["legal_elements"] = legal_elements

            enhanced_chunks.append(enhanced_chunk)

        return enhanced_chunks


class LlamaIndexSemanticChunker(ChunkingStrategy):
    """
    Semantic chunking strategy inspired by LlamaIndex's SemanticSplitterNodeParser.

    This chunker:
    1. First breaks text into sentences
    2. Creates embeddings for each sentence
    3. Identifies semantic breakpoints by measuring cosine similarity between adjacent embeddings
    4. Groups semantically similar sentences together
    5. Creates chunks based on semantic coherence rather than fixed token counts

    Based on: https://docs.llamaindex.ai/en/stable/examples/node_parsers/semantic_chunking/
    """

    def __init__(self,
                 buffer_size: int = 1,
                 breakpoint_percentile_threshold: int = 95,
                 min_chunk_size: int = 3,
                 max_chunk_size: int = 15):
        """
        Initialize the LlamaIndexSemanticChunker.

        Args:
            buffer_size: Number of sentences to consider on either side of potential breakpoints
            breakpoint_percentile_threshold: Percentile threshold to identify breakpoints (0-100)
            min_chunk_size: Minimum number of sentences per chunk
            max_chunk_size: Maximum number of sentences per chunk
        """
        super().__init__(f"llamaindex_semantic_{breakpoint_percentile_threshold}pct")
        self.buffer_size = buffer_size
        self.breakpoint_percentile_threshold = breakpoint_percentile_threshold
        self.min_chunk_size = min_chunk_size
        self.max_chunk_size = max_chunk_size

    def create_chunks(self, text: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Create chunks using semantic analysis of sentence embeddings.

        This method:
        1. Splits text into sentences
        2. Creates embeddings for each sentence
        3. Calculates cosine similarity between adjacent sentences
        4. Identifies semantic breakpoints based on similarity drops
        5. Creates chunks based on these breakpoints
        """
        if not has_transformers or embedding_model is None:
            print("Semantic chunking unavailable without sentence-transformers")
            return []

        chunks = []

        # Process the text with spaCy to get sentences
        doc = nlp(text)
        sentences = list(doc.sents)

        if len(sentences) <= self.min_chunk_size:
            # If text is too short, return as single chunk
            chunk_text = text
            chunks.append({
                "chunk_id": f"chunk_0",
                "text": chunk_text,
                "sentence_count": len(sentences),
                "token_count": count_tokens(chunk_text)
            })
            return chunks

        # Get sentence texts and create embeddings
        sentence_texts = [sent.text for sent in sentences]
        try:
            embeddings = embedding_model.encode(sentence_texts)
        except Exception as e:
            print(f"Error generating embeddings: {e}")
            # Fall back to simple sentence chunking if embeddings fail
            return SentenceChunker(sentences_per_chunk=5).create_chunks(text)

        # Calculate cosine similarity between adjacent sentences
        similarities = []
        for i in range(len(embeddings) - 1):
            # Get embeddings for current and next sentence
            curr_embedding = embeddings[i]
            next_embedding = embeddings[i + 1]

            # Calculate cosine similarity
            similarity = self._calculate_cosine_similarity(curr_embedding, next_embedding)
            similarities.append(similarity)

        # Find breakpoints based on similarity drops
        breakpoints = self._find_breakpoints(similarities)

        # Create chunks based on breakpoints
        chunk_ranges = self._get_chunk_ranges(breakpoints, len(sentences))

        # Create chunks from the identified ranges
        for i, (start_idx, end_idx) in enumerate(chunk_ranges):
            # Get sentences in this chunk
            chunk_sentences = sentences[start_idx:end_idx]

            # Create chunk text
            chunk_text = " ".join([sent.text for sent in chunk_sentences])

            # Skip empty chunks
            if not chunk_text.strip():
                continue

            # Create chunk object
            chunks.append({
                "chunk_id": f"chunk_{i}",
                "text": chunk_text,
                "sentence_count": len(chunk_sentences),
                "token_count": count_tokens(chunk_text),
                "start_sentence_idx": start_idx,
                "end_sentence_idx": end_idx
            })

        return chunks

    def _calculate_cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors."""
        # Normalize vectors to unit length
        vec1_norm = vec1 / np.linalg.norm(vec1)
        vec2_norm = vec2 / np.linalg.norm(vec2)

        # Compute cosine similarity
        return np.dot(vec1_norm, vec2_norm)

    def _find_breakpoints(self, similarities: List[float]) -> List[int]:
        """Find breakpoints based on similarity drops."""
        if not similarities:
            return []

        # Convert to numpy array for percentile calculation
        similarities_array = np.array(similarities)

        # Calculate the threshold for breakpoints
        # Lower similarity = higher dissimilarity = more likely breakpoint
        threshold = np.percentile(
            similarities_array, (100 - self.breakpoint_percentile_threshold)
        )

        # Find indices where similarity drops below threshold
        # Adding 1 because similarities[i] is between sentences i and i+1
        breakpoints = [i + 1 for i, sim in enumerate(similarities) if sim < threshold]

        # Apply buffer to avoid very small chunks
        filtered_breakpoints = []
        for i, bp in enumerate(breakpoints):
            # Check if there's a nearby breakpoint already added
            if (not filtered_breakpoints or
                bp - filtered_breakpoints[-1] >= self.min_chunk_size):
                filtered_breakpoints.append(bp)

        # Apply max chunk size constraint
        final_breakpoints = []
        last_bp = 0
        for bp in sorted(filtered_breakpoints):
            if bp - last_bp > self.max_chunk_size:
                # Add intermediate breakpoints to keep chunks under max size
                steps = (bp - last_bp) // self.max_chunk_size
                for i in range(1, steps + 1):
                    final_breakpoints.append(last_bp + i * self.max_chunk_size)
                last_bp = last_bp + steps * self.max_chunk_size
            else:
                final_breakpoints.append(bp)
                last_bp = bp

        return sorted(final_breakpoints)

    def _get_chunk_ranges(self, breakpoints: List[int], total_sentences: int) -> List[Tuple[int, int]]:
        """Convert breakpoints to chunk ranges (start_idx, end_idx)."""
        if not breakpoints:
            return [(0, total_sentences)]

        # Start with first chunk (from 0 to first breakpoint)
        ranges = [(0, breakpoints[0])]

        # Add middle chunks
        for i in range(len(breakpoints) - 1):
            ranges.append((breakpoints[i], breakpoints[i + 1]))

        # Add last chunk (from last breakpoint to end)
        ranges.append((breakpoints[-1], total_sentences))

        return ranges


class LegalRecursiveSemanticChunker(ChunkingStrategy):
    """
    A hybrid chunking strategy that combines recursive and semantic chunking,
    specifically designed for legal documents.

    This strategy:
    1. First chunks the text recursively, respecting structural elements
    2. Then refines chunks based on semantic similarity
    3. Adds special handling for legal-specific elements like definitions,
       citations, and numbered clauses
    4. Includes rolling window context for improved sentence context
    5. Enforces minimum character length for meaningful chunks
    """

    def __init__(self,
                 chunk_size: int = 500,
                 chunk_overlap: int = 50,
                 semantic_similarity_threshold: float = 0.7,
                 keep_legal_references_together: bool = True,
                 min_chunk_char_length: int = 50,
                 rolling_window_size: int = 1):
        super().__init__(f"legal_recursive_semantic_{chunk_size}tokens")
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.semantic_similarity_threshold = semantic_similarity_threshold
        self.keep_legal_references_together = keep_legal_references_together
        self.min_chunk_char_length = min_chunk_char_length
        self.rolling_window_size = rolling_window_size

        # Legal-specific separators in order of preference
        # Note the addition of legal-specific separators like § and numbered patterns
        self.separators = [
            "\n\n", "\n", "§",
            r"(?<=[.!?])\s+(?=[A-Z])",  # Sentence boundaries
            r"(?<=\d\.)\s+(?=[A-Z])",   # Numbered lists
            ".", ";", ":", "!", "?", ",", " ", ""
        ]

        # Patterns to identify legal references
        self.legal_reference_patterns = [
            re.compile(r'(?i)section\s+\d+'),
            re.compile(r'(?i)article\s+\d+'),
            re.compile(r'(?i)paragraph\s+\d+'),
            re.compile(r'(?i)clause\s+\d+'),
            re.compile(r'§\s*\d+'),
            re.compile(r'(?i)see\s+[^.;:!?]+')
        ]

    def identify_legal_elements(self, text: str) -> Dict[str, List[Tuple[int, int]]]:
        """
        Identify legal-specific elements in the text like definitions,
        citations, references, and numbered clauses.
        """
        legal_elements = {
            "definitions": [],
            "references": [],
            "numbered_clauses": []
        }

        # Find definitions (terms followed by "means" or similar)
        definition_pattern = re.compile(r'(?i)"([^"]+)"\s+(means|shall mean|is defined as|refers to)')
        for match in definition_pattern.finditer(text):
            legal_elements["definitions"].append(match.span())

        # Find legal references
        for pattern in self.legal_reference_patterns:
            for match in pattern.finditer(text):
                legal_elements["references"].append(match.span())

        # Find numbered clauses (common in legal documents)
        numbered_clause_pattern = re.compile(r'(?i)(\(\d+\)|\d+\.|\([a-z]\)|\(([ivx]+)\))\s+[A-Z]')
        for match in numbered_clause_pattern.finditer(text):
            legal_elements["numbered_clauses"].append(match.span())

        return legal_elements

    def split_text_recursive(self, text: str, separators: List[str], depth: int = 0) -> List[str]:
        """
        Recursively split text based on separators, with special handling for legal elements.
        """
        # Base case 1: Text is small enough
        if count_tokens(text) <= self.chunk_size:
            return [text]

        # Base case 2: No more separators left
        if depth >= len(separators):
            # If we reached here, we need to force-split the text
            return [text[:self.chunk_size]]

        # Get current separator
        separator = separators[depth]

        # Handle regex separators
        if separator.startswith('r'):
            # It's a regex pattern
            parts = re.split(separator[1:], text)
        else:
            # It's a literal separator
            parts = text.split(separator)

        # If we couldn't split (only one part) or parts are too big, try next separator
        if len(parts) == 1 or max(count_tokens(p) for p in parts if p.strip()) > self.chunk_size:
            return self.split_text_recursive(text, separators, depth + 1)

        # Process each part with remaining separators
        chunks = []
        for part in parts:
            if not part.strip():
                continue

            if count_tokens(part) > self.chunk_size:
                chunks.extend(self.split_text_recursive(part, separators, depth + 1))
            else:
                chunks.append(part)

        return chunks

    def should_keep_together(self, text: str) -> bool:
        """
        Determine if a text fragment should be kept together based on legal patterns.
        """
        # Keep definitions together
        if re.search(r'(?i)"([^"]+)"\s+means', text):
            return True

        # Keep entire clauses together when possible
        if re.search(r'(?i)^(\(\d+\)|\d+\.|\([a-z]\)|\(([ivx]+)\))\s+[A-Z]', text) and count_tokens(text) <= self.chunk_size * 1.2:
            return True

        # Keep references together
        for pattern in self.legal_reference_patterns:
            if pattern.search(text) and count_tokens(text) <= self.chunk_size * 1.2:
                return True

        return False

    def apply_semantic_refinement(self, chunks: List[str]) -> List[str]:
        """
        Refine chunks using semantic similarity to combine related chunks.
        """
        if not has_transformers or embedding_model is None:
            print("Semantic refinement unavailable without sentence-transformers")
            return chunks

        if len(chunks) <= 1:
            return chunks

        # Calculate embeddings for each chunk
        embeddings = embedding_model.encode(chunks)

        # Calculate cosine similarity between chunks
        from sklearn.metrics.pairwise import cosine_similarity
        similarity_matrix = cosine_similarity(embeddings)

        # Refine chunks by combining semantically similar ones
        refined_chunks = []
        skip_indices = set()

        for i in range(len(chunks)):
            if i in skip_indices:
                continue

            current_chunk = chunks[i]

            # Check if chunk meets minimum character length
            if len(current_chunk.strip()) < self.min_chunk_char_length:
                # Try to merge with the most similar chunk
                most_similar_idx = -1
                highest_similarity = -1

                for j in range(len(chunks)):
                    if j != i and j not in skip_indices:
                        if similarity_matrix[i, j] > highest_similarity:
                            highest_similarity = similarity_matrix[i, j]
                            most_similar_idx = j

                if most_similar_idx != -1:
                    combined = current_chunk + " " + chunks[most_similar_idx]
                    if count_tokens(combined) <= self.chunk_size * 1.2:
                        current_chunk = combined
                        skip_indices.add(most_similar_idx)

            # Look for semantically similar chunks to combine
            for j in range(i + 1, len(chunks)):
                if j in skip_indices:
                    continue

                # Check if chunks are semantically similar
                if similarity_matrix[i, j] > self.semantic_similarity_threshold:
                    # Check if combined chunk would be too large
                    combined = current_chunk + " " + chunks[j]
                    if count_tokens(combined) <= self.chunk_size * 1.5:
                        current_chunk = combined
                        skip_indices.add(j)

            refined_chunks.append(current_chunk)

        return refined_chunks

    def apply_rolling_window(self, document_sentences: List, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Apply rolling window context to chunks by adding surrounding sentences.

        Args:
            document_sentences: List of all sentences in the document
            chunks: List of chunk dictionaries

        Returns:
            List of enhanced chunks with rolling window context
        """
        enhanced_chunks = []

        for chunk in chunks:
            # Try to find the chunk sentences in the original document
            chunk_text = chunk["text"]
            found_indices = []

            # This is a simple approach - for a production system, you would need
            # a more sophisticated algorithm to locate chunks in the original document
            for i, sentence in enumerate(document_sentences):
                if sentence.text in chunk_text:
                    found_indices.append(i)

            # If we found the sentences, add context from surrounding sentences
            if found_indices:
                start_idx = max(0, min(found_indices) - self.rolling_window_size)
                end_idx = min(len(document_sentences) - 1, max(found_indices) + self.rolling_window_size)

                # Get context sentences
                context_before = " ".join([s.text for s in document_sentences[start_idx:min(found_indices)]])
                context_after = " ".join([s.text for s in document_sentences[max(found_indices) + 1:end_idx + 1]])

                # Create enhanced chunk with context
                enhanced_chunk = chunk.copy()
                if context_before:
                    enhanced_chunk["context_before"] = context_before
                if context_after:
                    enhanced_chunk["context_after"] = context_after

                enhanced_chunks.append(enhanced_chunk)
            else:
                # If we couldn't locate the chunk, just keep it as is
                enhanced_chunks.append(chunk)

        return enhanced_chunks

    def create_chunks(self, text: str, **kwargs) -> List[Dict[str, Any]]:
        # Process the entire document with spaCy to get all sentences
        doc = nlp(text)
        all_sentences = list(doc.sents)

        chunks = []

        # First identify document sections
        sections = identify_document_sections(text)

        # Identify legal elements
        legal_elements = self.identify_legal_elements(text)

        # Process each section type differently
        section_types = list(sections.keys()) + ["legal_specific"]

        # Process the document by sections
        for section_type in section_types:
            if section_type == "legal_specific":
                # For legal-specific elements, extract and process them separately
                legal_texts = []
                for element_type, spans in legal_elements.items():
                    for start, end in spans:
                        legal_texts.append((text[start:end], element_type))

                for legal_text, element_type in legal_texts:
                    if not legal_text.strip() or len(legal_text.strip()) < self.min_chunk_char_length:
                        continue

                    # Special handling for definitions
                    if element_type == "definitions" and count_tokens(legal_text) <= self.chunk_size:
                        chunks.append({
                            "chunk_id": f"chunk_{len(chunks)}",
                            "text": legal_text,
                            "section_type": "definition",
                            "token_count": count_tokens(legal_text),
                            "element_type": element_type
                        })
                    else:
                        # Apply recursive chunking to the legal element
                        element_chunks = self.split_text_recursive(legal_text, self.separators)

                        # Convert to proper chunk objects
                        for chunk_text in element_chunks:
                            if not chunk_text.strip() or len(chunk_text.strip()) < self.min_chunk_char_length:
                                continue

                            chunks.append({
                                "chunk_id": f"chunk_{len(chunks)}",
                                "text": chunk_text,
                                "section_type": section_type,
                                "token_count": count_tokens(chunk_text),
                                "element_type": element_type
                            })
            elif section_type in sections:
                # Process regular document sections
                for start, end in sections[section_type]:
                    section_text = text[start:end]
                    if not section_text.strip() or len(section_text.strip()) < self.min_chunk_char_length:
                        continue

                    # Apply recursive splitting
                    section_chunks = self.split_text_recursive(section_text, self.separators)

                    # Apply semantic refinement if possible
                    if has_transformers and embedding_model is not None:
                        section_chunks = self.apply_semantic_refinement(section_chunks)

                    # Convert to proper chunk objects
                    for chunk_text in section_chunks:
                        if not chunk_text.strip() or len(chunk_text.strip()) < self.min_chunk_char_length:
                            continue

                        chunks.append({
                            "chunk_id": f"chunk_{len(chunks)}",
                            "text": chunk_text,
                            "section_type": section_type,
                            "token_count": count_tokens(chunk_text)
                        })

        # Deduplicate chunks
        unique_chunks = {}
        for chunk in chunks:
            text = chunk["text"]
            if text not in unique_chunks:
                unique_chunks[text] = chunk

        # Apply rolling window context if sentences are available
        final_chunks = list(unique_chunks.values())
        if all_sentences and self.rolling_window_size > 0:
            final_chunks = self.apply_rolling_window(all_sentences, final_chunks)

        # Renumber chunks to ensure sequential IDs
        for i, chunk in enumerate(final_chunks):
            chunk["chunk_id"] = f"chunk_{i}"

        return final_chunks


class SemanticDoubleMergingChunker(ChunkingStrategy):
    """
    Semantic double merging chunking strategy based on LlamaIndex's SemanticDoubleMergingSplitterNodeParser.

    This chunker:
    1. Initially breaks text into sentences (or smaller paragraphs)
    2. Performs first merge pass: merges initial chunks based on semantic similarity threshold
    3. Performs second merge pass: appends smaller chunks to their most similar neighbors
    4. Respects maximum chunk size constraints throughout merging

    Based on: https://docs.llamaindex.ai/en/stable/examples/node_parsers/semantic_double_merging_chunking/
    """

    def __init__(self,
                 initial_threshold: float = 0.8,
                 appending_threshold: float = 0.8,
                 merging_threshold: float = 0.8,
                 max_chunk_size: int = 500):
        """
        Initialize the SemanticDoubleMergingChunker.

        Args:
            initial_threshold: Similarity threshold for initial sentence merging
                               (higher means less merging)
            appending_threshold: Similarity threshold for appending smaller chunks
                                 (higher means less appending)
            merging_threshold: Similarity threshold for final merging of chunks
                               (higher means less merging)
            max_chunk_size: Maximum size of a chunk in tokens
        """
        super().__init__(f"semantic_double_merging_t{initial_threshold}_{merging_threshold}")
        self.initial_threshold = initial_threshold
        self.appending_threshold = appending_threshold
        self.merging_threshold = merging_threshold
        self.max_chunk_size = max_chunk_size

    def create_chunks(self, text: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Create chunks using the semantic double merging approach.

        This method:
        1. Splits text into initial sentences or paragraphs
        2. Performs first merge pass (initial merging)
        3. Performs second merge pass (appending of small chunks)
        4. Creates final chunks from the merged text segments
        """
        if not has_transformers or embedding_model is None:
            print("Semantic double merging unavailable without sentence-transformers")
            return []

        chunks = []

        # Process the text with spaCy to get better sentence boundaries
        doc = nlp(text)

        # Get initial text segments (prefer paragraph breaks first, then sentences)
        # This replicates how LlamaIndex's splitter works with spacy
        paragraphs = self._split_into_paragraphs(text)

        if not paragraphs or sum(len(p) for p in paragraphs) < len(text) * 0.5:
            # If paragraphs splitting didn't work well, use sentences
            initial_segments = [sent.text for sent in doc.sents]
        else:
            initial_segments = paragraphs

        # Skip if too few segments
        if len(initial_segments) <= 1:
            chunks.append({
                "chunk_id": "chunk_0",
                "text": text,
                "token_count": count_tokens(text),
                "is_single_segment": True
            })
            return chunks

        # Compute embeddings for all initial segments
        try:
            initial_embeddings = embedding_model.encode(initial_segments)
        except Exception as e:
            print(f"Error generating embeddings: {e}")
            # Fall back to simple sentence chunking if embeddings fail
            return SentenceChunker(sentences_per_chunk=5).create_chunks(text)

        # First pass: Initial merging of segments
        merged_segments, merged_embeddings = self._perform_initial_merging(
            initial_segments, initial_embeddings
        )

        # Second pass: Appending small chunks to their most similar neighbors
        final_segments = self._append_small_chunks(
            merged_segments, merged_embeddings
        )

        # Create the final chunks
        for i, segment in enumerate(final_segments):
            if not segment.strip():
                continue

            # Create chunk object
            chunk = {
                "chunk_id": f"chunk_{i}",
                "text": segment,
                "token_count": count_tokens(segment),
            }

            # Add to chunks
            chunks.append(chunk)

        return chunks

    def _split_into_paragraphs(self, text: str) -> List[str]:
        """Split text into paragraphs based on newlines."""
        # Split by double newlines first (strongest paragraph indicator)
        paragraphs = text.split('\n\n')

        # If that didn't yield multiple paragraphs, try single newlines
        if len(paragraphs) <= 1:
            paragraphs = text.split('\n')

        # Filter out empty paragraphs
        paragraphs = [p.strip() for p in paragraphs if p.strip()]

        return paragraphs

    def _perform_initial_merging(self, segments: List[str], embeddings: np.ndarray) -> Tuple[List[str], np.ndarray]:
        """
        Performs first pass of merging: combine adjacent segments that are semantically similar.

        Args:
            segments: List of text segments
            embeddings: Numpy array of embeddings for each segment

        Returns:
            Tuple of (merged_segments, merged_embeddings)
        """
        if len(segments) <= 1:
            return segments, embeddings

        merged_segments = []
        merged_embeddings = []

        current_segment = segments[0]
        current_embedding = embeddings[0].reshape(1, -1)
        current_tokens = count_tokens(current_segment)

        i = 1
        while i < len(segments):
            # Calculate similarity between current merged segment and next segment
            similarity = self._calculate_cosine_similarity(
                current_embedding.mean(axis=0),
                embeddings[i]
            )

            next_segment = segments[i]
            next_tokens = count_tokens(next_segment)

            # Merge if similarity is high enough and won't exceed max size
            if (similarity >= self.initial_threshold and
                current_tokens + next_tokens <= self.max_chunk_size):
                # Merge segments
                current_segment += " " + next_segment
                current_tokens += next_tokens

                # Update embedding (average of all merged embeddings)
                current_embedding = np.vstack([current_embedding, embeddings[i].reshape(1, -1)])
            else:
                # Add current segment to results and start a new one
                merged_segments.append(current_segment)
                merged_embeddings.append(current_embedding.mean(axis=0))

                current_segment = next_segment
                current_embedding = embeddings[i].reshape(1, -1)
                current_tokens = next_tokens

            i += 1

        # Add the last segment
        merged_segments.append(current_segment)
        merged_embeddings.append(current_embedding.mean(axis=0))

        return merged_segments, np.array(merged_embeddings)

    def _append_small_chunks(self, segments: List[str], embeddings: np.ndarray) -> List[str]:
        """
        Performs second pass of merging: append small chunks to their most similar neighbor.

        Args:
            segments: List of text segments after initial merging
            embeddings: Numpy array of embeddings for each merged segment

        Returns:
            List of final text segments after appending
        """
        if len(segments) <= 1:
            return segments

        # Calculate token counts for each segment
        token_counts = [count_tokens(segment) for segment in segments]

        # Calculate average token count
        avg_tokens = sum(token_counts) / len(token_counts)

        # Identify small chunks (less than 50% of average)
        small_chunks = []
        for i, count in enumerate(token_counts):
            if count < avg_tokens * 0.5:
                small_chunks.append(i)

        # If no small chunks, return original segments
        if not small_chunks:
            return segments

        # Calculate pairwise similarities between all segments
        num_segments = len(segments)
        similarity_matrix = np.zeros((num_segments, num_segments))

        for i in range(num_segments):
            for j in range(num_segments):
                if i != j:
                    similarity_matrix[i, j] = self._calculate_cosine_similarity(
                        embeddings[i], embeddings[j]
                    )

        # Keep track of which segments have been merged
        merged_flags = [False] * num_segments
        final_segments = []

        # First, handle appending small chunks to their most similar neighbors
        for i in small_chunks:
            if merged_flags[i]:
                continue

            # Find most similar segment (exclude self)
            similarity_scores = similarity_matrix[i]
            similarity_scores[i] = -1  # Exclude self

            # Also exclude already merged segments
            for j in range(num_segments):
                if merged_flags[j]:
                    similarity_scores[j] = -1

            most_similar_idx = np.argmax(similarity_scores)
            max_similarity = similarity_scores[most_similar_idx]

            # Only merge if similarity is above threshold
            if max_similarity >= self.appending_threshold:
                # Check if combining wouldn't exceed max token limit
                combined_tokens = token_counts[i] + token_counts[most_similar_idx]
                if combined_tokens <= self.max_chunk_size:
                    # Append the small chunk to the most similar segment
                    if i < most_similar_idx:
                        merged_text = segments[i] + " " + segments[most_similar_idx]
                    else:
                        merged_text = segments[most_similar_idx] + " " + segments[i]

                    # Mark both as merged
                    merged_flags[i] = True
                    merged_flags[most_similar_idx] = True

                    # Add to final segments
                    final_segments.append(merged_text)

        # Add remaining unmerged segments
        for i in range(num_segments):
            if not merged_flags[i]:
                final_segments.append(segments[i])

        return final_segments

    def _calculate_cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors."""
        # Normalize vectors to unit length
        vec1_norm = vec1 / np.linalg.norm(vec1)
        vec2_norm = vec2 / np.linalg.norm(vec2)

        # Compute cosine similarity
        return np.dot(vec1_norm, vec2_norm)


# Functions for analyzing and comparing chunking results
def analyze_chunks(chunks: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze chunks and return statistics"""
    if not chunks:
        return {"error": "No chunks to analyze"}

    token_counts = [chunk.get("token_count", 0) for chunk in chunks]

    return {
        "total_chunks": len(chunks),
        "avg_token_count": sum(token_counts) / len(chunks) if token_counts else 0,
        "min_tokens": min(token_counts) if token_counts else 0,
        "max_tokens": max(token_counts) if token_counts else 0,
    }


def evaluate_chunking_for_key_phrases(chunks: List[Dict[str, Any]], key_phrases: List[str]) -> Dict[str, Any]:
    """
    Evaluate how well the chunking strategy captures key phrases.
    This helps identify if important short sentences are properly preserved.
    """
    results = {}

    for phrase in key_phrases:
        found = False
        chunk_ids = []

        for chunk in chunks:
            chunk_text = chunk.get("text", "")

            if phrase.lower() in chunk_text.lower():
                found = True
                chunk_ids.append(chunk.get("chunk_id", "unknown"))

        results[phrase] = {
            "found": found,
            "occurrences": len(chunk_ids),
            "chunk_ids": chunk_ids
        }

    return results


# Add a function to perform BM25 search on chunks
def perform_bm25_search(chunks: List[Dict[str, Any]], query: str, top_k: int = 5) -> List[Tuple[Dict[str, Any], float]]:
    """
    Performs BM25 search on the given chunks using the query.
    Returns a list of tuples containing (chunk, score).
    """
    if not has_bm25:
        print("BM25 search is not available. Please install rank-bm25.")
        return []

    if not chunks:
        print("No chunks to search.")
        return []

    # Extract chunk texts
    chunk_texts = [chunk.get("text", "") for chunk in chunks]

    # Tokenize chunks and query
    tokenized_corpus = [text.lower().split() for text in chunk_texts]
    tokenized_query = query.lower().split()

    # Create BM25 model
    bm25 = BM25Okapi(tokenized_corpus)

    # Get scores
    scores = bm25.get_scores(tokenized_query)

    # Pair chunks with scores and sort by score (descending)
    chunk_scores = list(zip(chunks, scores))
    chunk_scores.sort(key=lambda x: x[1], reverse=True)

    # Return top k results
    return chunk_scores[:top_k]


# Add a function to run BM25 search across all strategy chunks
def run_bm25_search_on_all_strategies(strategies: List[ChunkingStrategy], full_text: str, query: str, top_k: int = 5):
    """
    Runs BM25 search on chunks from all strategies and saves the results.
    """
    print(f"\nRunning BM25 search for query: '{query}' across all chunking strategies...")

    results = {}

    for strategy in strategies:
        print(f"\nSearching chunks from {strategy.name} strategy...")
        chunks = strategy.create_chunks(full_text)
        search_results = perform_bm25_search(chunks, query, top_k)

        # Print results
        print(f"Top {len(search_results)} results:")
        strategy_results = []

        for i, (chunk, score) in enumerate(search_results):
            print(f"Result {i+1}: Chunk {chunk.get('chunk_id')} (Score: {score:.4f})")
            print(f"Text: {chunk.get('text', '')[:150]}...")

            strategy_results.append({
                "chunk_id": chunk.get("chunk_id"),
                "text": chunk.get("text"),
                "score": float(score),
                "token_count": chunk.get("token_count", 0),
                "position": i+1
            })

        results[strategy.name] = strategy_results

    # Save results
    output_dir = os.path.join(RESULTS_DIR)
    os.makedirs(output_dir, exist_ok=True)

    query_filename = query.replace(" ", "_").replace("?", "").lower()[:30]
    output_path = os.path.join(output_dir, f"bm25_search_{query_filename}.json")

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump({
            "query": query,
            "top_k": top_k,
            "results": results
        }, f, indent=2)

    print(f"\nSaved BM25 search results to {output_path}")
    return results


# Modify the main function to add user query input
def main():
    # Check if test file exists
    test_files = [f for f in os.listdir(TEST_FILE_DIR) if f.endswith('.pdf')]
    txt_files = [f for f in os.listdir(TEST_FILE_DIR) if f.endswith('.txt')]

    # Display information about the chunking strategies
    print("\n" + "="*60)
    print("LEGAL DOCUMENT CHUNKING TEST FRAMEWORK")
    print("="*60)
    print("This framework tests various chunking strategies including our new hybrid approach:")
    print("  - LegalRecursiveSemanticChunker: A specialized hybrid chunker that combines")
    print("    recursive chunking (for document structure) with semantic analysis")
    print("    and specialized handling for legal elements like definitions, citations,")
    print("    and numbered clauses. This approach is designed to preserve legal context")
    print("    while creating optimal chunks for retrieval.")
    print("="*60 + "\n")

    if test_files:
        test_file = os.path.join(TEST_FILE_DIR, test_files[0])
        print(f"Processing PDF file: {test_file}")
        full_text, page_texts = extract_text_from_pdf(test_file)
    elif txt_files:
        test_file = os.path.join(TEST_FILE_DIR, txt_files[0])
        print(f"Processing text file: {test_file}")
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                full_text = f.read()
                page_texts = {0: full_text}  # Simulate page text
        except Exception as e:
            print(f"Failed to read text file: {e}")
            return
    else:
        print(f"No test files found in {TEST_FILE_DIR}. Please add a test.pdf or text file.")
        return

    if not full_text:
        print("Failed to extract text from file.")
        return

    print(f"Extracted text: {len(full_text)} characters, {count_tokens(full_text)} tokens")

    # Define chunking strategies to test
    strategies = [
        FixedSizeChunker(chunk_size=500, chunk_overlap=50),
        SentenceChunker(sentences_per_chunk=3),
        RecursiveChunker(chunk_size=500, chunk_overlap=50),
        RollingWindowChunker(window_size=3, step_size=1),
        HybridSectionChunker(),
        ProductionChunker(sentences_per_chunk=3, min_chunk_char_length=50),
        ProductionV2Chunker(sentences_per_chunk=3, min_chunk_char_length=50),
        LegalRecursiveSemanticChunker(
            chunk_size=500,
            chunk_overlap=100,
            semantic_similarity_threshold=0.7,
            min_chunk_char_length=50,
            rolling_window_size=1
        ),
        LlamaIndexSemanticChunker(
            buffer_size=1,
            breakpoint_percentile_threshold=95,
            min_chunk_size=3,
            max_chunk_size=15
        ),
        SemanticDoubleMergingChunker(
            initial_threshold=0.8,
            appending_threshold=0.8,
            merging_threshold=0.8,
            max_chunk_size=500
        ),
    ]

    # Add semantic chunking if available
    if has_transformers and embedding_model is not None:
        strategies.append(SemanticChunker())

    # Process the document with each strategy
    for strategy in strategies:
        print(f"\nApplying {strategy.name} chunking strategy...")
        chunks = strategy.create_chunks(full_text)
        strategy.save_results(chunks, os.path.basename(test_file).split('.')[0])

        # Print basic analysis
        analysis = analyze_chunks(chunks)
        print(f"Created {analysis['total_chunks']} chunks.")
        print(f"Average token count: {analysis['avg_token_count']:.1f}")
        print(f"Token range: {analysis['min_tokens']} to {analysis['max_tokens']}")

    # Test with key legal phrases to evaluate effectiveness
    # These are examples - you would replace with actual phrases from your doc
    key_phrases = [
        "Dollars",
        "$",
        "lawful currency of the United States of America",
        "means",
        "shall mean",
        "defined term",
        # Additional legal-specific phrases
        "pursuant to",
        "hereinafter",
        "aforementioned",
        "section",
        "article",
        "clause",
        "subsection",
        "notwithstanding",
        "whereas",
        "in accordance with",
        "subject to",
        "§",
        "governing law",
        "jurisdiction"
    ]

    print("\nEvaluating all strategies for key phrase detection...")
    for strategy in strategies:
        chunks = strategy.create_chunks(full_text)
        phrase_results = evaluate_chunking_for_key_phrases(chunks, key_phrases)

        found_count = sum(1 for result in phrase_results.values() if result["found"])
        print(f"\n{strategy.name}: Found {found_count}/{len(key_phrases)} key phrases")

        # Save phrase evaluation results
        output_path = os.path.join(RESULTS_DIR, f"phrase_eval_{strategy.name}.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump({
                "strategy": strategy.name,
                "key_phrases": phrase_results
            }, f, indent=2)

    # New step: Ask user for a query and run BM25 search
    if has_bm25:
        print("\n" + "="*60)
        print("BM25 SEARCH")
        print("="*60)

        try:
            query = input("\nEnter your search query (e.g., 'What is the currency of the agreement?' or 'Who has jurisdiction in case of dispute?'): ")
            if query:
                run_bm25_search_on_all_strategies(strategies, full_text, query)
            else:
                print("No query entered. Skipping BM25 search.")
        except KeyboardInterrupt:
            print("\nBM25 search cancelled by user.")
        except Exception as e:
            print(f"Error during BM25 search: {e}")
    else:
        print("\nBM25 search is not available. Please install rank-bm25 to enable this feature.")

    # Note about enhanced retrieval
    print("\n" + "="*60)
    print("ENHANCED RETRIEVAL EVALUATION")
    print("="*60)
    print("For more advanced retrieval evaluation and comparison, including:")
    print("  - Semantic search and hybrid search capabilities")
    print("  - Performance visualization and metrics")
    print("  - Interactive search interface")
    print("\nRun the enhanced retrieval module with:")
    print("  python enhanced_retrieval.py")

    # Note about semantic evaluation
    print("\n" + "="*60)
    print("SEMANTIC EVALUATION")
    print("="*60)
    print("For ground-truth based evaluation of chunking strategies, including:")
    print("  - Testing against predefined legal queries with known answers")
    print("  - Measuring semantic similarity to correct answers")
    print("  - Objective comparison and ranking of strategies")
    print("\nRun the semantic evaluation module with:")
    print("  python semantic_evaluation.py")


if __name__ == "__main__":
    main()