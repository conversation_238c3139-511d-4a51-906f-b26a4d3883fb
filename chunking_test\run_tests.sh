#!/bin/bash

# Shell script to set up and run the chunking tests

# Ensure we're in the right directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

echo "Setting up test environment..."

# Create virtual environment
echo "Creating virtual environment..."
python -m venv .venv 2>/dev/null || python3 -m venv .venv 2>/dev/null
if [ $? -eq 0 ]; then
    echo "Virtual environment created."
    
    # Activate virtual environment
    echo "Activating virtual environment..."
    if [ -f ".venv/bin/activate" ]; then
        source .venv/bin/activate
    else
        echo "Activation script not found. Virtual environment may not have been created correctly."
        exit 1
    fi
else
    echo "Failed to create virtual environment. Continuing without it."
fi

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt || pip3 install -r requirements.txt

# Install additional visualization dependencies
echo "Installing matplotlib for visualizations..."
pip install matplotlib || pip3 install matplotlib

# Install Spacy model
echo "Downloading spaCy model..."
python -m spacy download en_core_web_sm || python3 -m spacy download en_core_web_sm

# Download NLTK data
echo "Downloading NLTK data..."
python -c "import nltk; nltk.download('punkt')" || python3 -c "import nltk; nltk.download('punkt')"

# Check if test PDF exists, if not, remind user to add it
if [ ! "$(ls -A test_file/*.pdf 2>/dev/null)" ]; then
    echo ""
    echo "NOTE: No PDF file found in the test_file directory."
    echo "The system will use the text file for testing, but for a complete test, you should convert"
    echo "sample_legal_document.txt to PDF and place it in the test_file directory."
    echo ""
fi

# Run the tests
echo "Running chunking tests..."
echo "================================="
python chunking_test.py || python3 chunking_test.py

# Run the definition test
echo ""
echo "Running definition chunking analysis..."
echo "================================="
python test_challenge.py || python3 test_challenge.py

# Run integration test
echo ""
echo "Running integration test..."
echo "================================="
python integrate_with_retrieval.py || python3 integrate_with_retrieval.py

# Run enhanced retrieval evaluation
echo ""
echo "Running enhanced retrieval evaluation..."
echo "================================="
python enhanced_retrieval.py || python3 enhanced_retrieval.py

# Run semantic evaluation
echo ""
echo "Running semantic evaluation..."
echo "================================="
python semantic_evaluation.py || python3 semantic_evaluation.py

echo ""
echo "All tests completed. Results are in the 'results' directory." 