"""
Semantic Evaluation Module for Chunking Strategies

This module provides tools to semantically evaluate the quality of chunks 
against ground truth answers for specific legal queries. It helps determine
which chunking strategy is most effective at preserving the semantic meaning
needed to answer specific questions.
"""

import os
import sys
import json
import time
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from collections import defaultdict

# Add parent directory to path to import from main project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import from our chunking test modules
try:
    from chunking_test.chunking_test import (
        extract_text_from_pdf,
        ChunkingStrategy,
        SentenceChunker,
        HybridSectionChunker,
        RollingWindowChunker,
        ProductionChunker,
        FixedSizeChunker,
        RecursiveChunker,
        SemanticChunker,
        perform_bm25_search,
        count_tokens,
        RESULTS_DIR
    )
    from chunking_test.enhanced_retrieval import EnhancedRetrieval
except ImportError:
    # Try alternative import path
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    try:
        from chunking_test import (
            extract_text_from_pdf,
            ChunkingStrategy,
            SentenceChunker,
            HybridSectionChunker,
            RollingWindowChunker,
            ProductionChunker,
            FixedSizeChunker,
            RecursiveChunker,
            SemanticChunker,
            perform_bm25_search,
            count_tokens,
            RESULTS_DIR
        )
        from enhanced_retrieval import EnhancedRetrieval
    except ImportError:
        print("Error: Could not import required modules. Make sure chunking_test.py and enhanced_retrieval.py are in the current directory.")
        sys.exit(1)

# Check for required libraries
try:
    from sentence_transformers import SentenceTransformer, util
    import torch
    has_transformers = True
except ImportError:
    has_transformers = False
    print("Warning: sentence-transformers not installed. Semantic evaluation will be limited.")

try:
    import matplotlib.pyplot as plt
    has_matplotlib = True
except ImportError:
    has_matplotlib = False
    print("Warning: matplotlib not installed. Visualization will be disabled.")


class SemanticEvaluator:
    """
    Evaluates how well different chunking strategies preserve the semantic 
    information needed to answer legal questions.
    """
    
    def __init__(self, embedding_model=None):
        """Initialize with an optional embedding model for semantic comparison"""
        self.embedding_model = embedding_model
        if has_transformers and self.embedding_model is None:
            try:
                self.embedding_model = SentenceTransformer("all-MiniLM-L6-v2")
                print("Loaded sentence embeddings model for semantic evaluation")
            except Exception as e:
                print(f"Error loading default embedding model: {e}")
        
        self.retrieval = EnhancedRetrieval(embedding_model=self.embedding_model)
    
    def semantic_similarity(self, text1: str, text2: str) -> float:
        """
        Calculate semantic similarity between two texts using embeddings.
        Returns a score between 0 and 1, where 1 is perfect similarity.
        """
        if not has_transformers or self.embedding_model is None:
            print("Semantic similarity calculation is not available without sentence-transformers.")
            return 0.0
        
        # Encode the texts
        embedding1 = self.embedding_model.encode(text1, convert_to_tensor=True)
        embedding2 = self.embedding_model.encode(text2, convert_to_tensor=True)
        
        # Calculate cosine similarity
        cos_score = util.cos_sim(embedding1, embedding2).item()
        
        return cos_score
    
    def evaluate_against_ground_truth(
        self, 
        strategies: List[ChunkingStrategy], 
        full_text: str,
        ground_truth_data: List[Dict[str, str]],
        retrieval_method: str = "hybrid",
        top_k: int = 3
    ) -> Dict[str, Any]:
        """
        Evaluate chunking strategies by measuring how well the retrieved chunks 
        can answer specific questions compared to ground truth answers.
        
        Args:
            strategies: List of chunking strategies to evaluate
            full_text: Document text
            ground_truth_data: List of dicts with 'query' and 'answer' keys
            retrieval_method: "bm25", "semantic", or "hybrid"
            top_k: Number of top chunks to retrieve
            
        Returns:
            Dictionary with evaluation results
        """
        if not has_transformers or self.embedding_model is None:
            print("Semantic evaluation requires sentence-transformers. Limited evaluation will be performed.")
        
        results = {
            "method": retrieval_method,
            "top_k": top_k,
            "strategies": {},
            "summary": {},
            "ground_truth": ground_truth_data
        }
        
        # Process each strategy
        for strategy in strategies:
            print(f"\nEvaluating {strategy.name} strategy against ground truth...")
            chunks = strategy.create_chunks(full_text)
            
            strategy_results = []
            
            # Test each ground truth query-answer pair
            for item in ground_truth_data:
                query = item["query"]
                ground_truth = item["answer"]
                
                # Retrieve chunks using specified method
                if retrieval_method == "bm25":
                    retrieved = perform_bm25_search(chunks, query, top_k)
                elif retrieval_method == "semantic":
                    retrieved = self.retrieval.perform_semantic_search(chunks, query, top_k)
                else:  # hybrid
                    retrieved = self.retrieval.perform_hybrid_search(chunks, query, top_k)
                
                # Calculate similarity between retrieved chunks and ground truth
                chunk_similarities = []
                for chunk, score in retrieved:
                    chunk_text = chunk.get("text", "")
                    sim_score = self.semantic_similarity(chunk_text, ground_truth)
                    chunk_similarities.append({
                        "chunk_id": chunk.get("chunk_id", "unknown"),
                        "retrieval_score": float(score),
                        "semantic_similarity": float(sim_score),
                        "text_preview": chunk_text[:200] + "..." if len(chunk_text) > 200 else chunk_text
                    })
                
                # Calculate best match and average similarity
                best_sim = max([s["semantic_similarity"] for s in chunk_similarities]) if chunk_similarities else 0.0
                avg_sim = sum([s["semantic_similarity"] for s in chunk_similarities]) / len(chunk_similarities) if chunk_similarities else 0.0
                
                # Save results for this query
                query_result = {
                    "query": query,
                    "ground_truth": ground_truth,
                    "best_similarity": float(best_sim),
                    "avg_similarity": float(avg_sim),
                    "retrieved_chunks": chunk_similarities
                }
                strategy_results.append(query_result)
                
                print(f"  Query: '{query}'")
                print(f"  Best semantic similarity: {best_sim:.4f}, Avg: {avg_sim:.4f}")
            
            # Calculate overall stats for this strategy
            avg_best_sim = sum([r["best_similarity"] for r in strategy_results]) / len(strategy_results) if strategy_results else 0.0
            avg_avg_sim = sum([r["avg_similarity"] for r in strategy_results]) / len(strategy_results) if strategy_results else 0.0
            
            # Store results for this strategy
            results["strategies"][strategy.name] = {
                "query_results": strategy_results,
                "avg_best_similarity": float(avg_best_sim),
                "avg_avg_similarity": float(avg_avg_sim)
            }
            
            print(f"  Overall: Average best similarity: {avg_best_sim:.4f}, Average of averages: {avg_avg_sim:.4f}")
        
        # Create summary of results across all strategies
        summary = {}
        for strategy_name, strategy_result in results["strategies"].items():
            summary[strategy_name] = {
                "avg_best_similarity": strategy_result["avg_best_similarity"],
                "avg_avg_similarity": strategy_result["avg_avg_similarity"]
            }
        
        results["summary"] = summary
        
        return results
    
    def save_evaluation_results(self, results: Dict[str, Any], filename_prefix: str = "semantic_evaluation"):
        """Save semantic evaluation results to a file"""
        os.makedirs(RESULTS_DIR, exist_ok=True)
        
        method = results.get("method", "unknown")
        output_path = os.path.join(RESULTS_DIR, f"{filename_prefix}_{method}.json")
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2)
        
        print(f"Saved semantic evaluation results to {output_path}")
        
        # Generate visualization if matplotlib is available
        if has_matplotlib:
            self.generate_evaluation_visualization(results, output_path.replace(".json", ".png"))
        
        return output_path
    
    def generate_evaluation_visualization(self, results: Dict[str, Any], output_path: str):
        """Generate visualization of semantic evaluation results"""
        if not has_matplotlib:
            print("Matplotlib is not available. Skipping visualization.")
            return
        
        try:
            strategies = list(results["strategies"].keys())
            
            # Create figure
            plt.figure(figsize=(12, 8))
            
            # Plot average best semantic similarity
            ax1 = plt.subplot(2, 1, 1)
            best_sims = [results["strategies"][strategy]["avg_best_similarity"] for strategy in strategies]
            bars = ax1.bar(strategies, best_sims)
            
            # Add value labels on bars
            for bar in bars:
                height = bar.get_height()
                ax1.annotate(f'{height:.3f}',
                            xy=(bar.get_x() + bar.get_width() / 2, height),
                            xytext=(0, 3),  # 3 points vertical offset
                            textcoords="offset points",
                            ha='center', va='bottom')
            
            ax1.set_title("Average Best Semantic Similarity by Strategy")
            ax1.set_ylabel("Semantic Similarity")
            ax1.set_xticklabels(strategies, rotation=45, ha="right")
            ax1.set_ylim(0, 1.0)  # Cosine similarity is between 0 and 1
            plt.tight_layout()
            
            # Plot average of average semantic similarities
            ax2 = plt.subplot(2, 1, 2)
            avg_sims = [results["strategies"][strategy]["avg_avg_similarity"] for strategy in strategies]
            bars = ax2.bar(strategies, avg_sims)
            
            # Add value labels on bars
            for bar in bars:
                height = bar.get_height()
                ax2.annotate(f'{height:.3f}',
                            xy=(bar.get_x() + bar.get_width() / 2, height),
                            xytext=(0, 3),  # 3 points vertical offset
                            textcoords="offset points",
                            ha='center', va='bottom')
            
            ax2.set_title("Average of Average Semantic Similarities by Strategy")
            ax2.set_ylabel("Semantic Similarity")
            ax2.set_xticklabels(strategies, rotation=45, ha="right")
            ax2.set_ylim(0, 1.0)  # Cosine similarity is between 0 and 1
            plt.tight_layout()
            
            # Save the figure
            plt.savefig(output_path, dpi=300, bbox_inches="tight")
            plt.close()
            
            print(f"Saved visualization to {output_path}")
        except Exception as e:
            print(f"Error generating visualization: {e}")


def create_legal_ground_truth_data():
    """
    Create a set of legal document questions and ground truth answers
    specifically designed to test chunking strategies.
    """
    return [
        {
            "query": "What is the definition of Dollars?",
            "answer": "\"Dollars\" or \"$\" means the lawful currency of the United States of America."
        },
        {
            "query": "Who is the Borrower in the agreement?",
            "answer": "ACME CORPORATION, a Delaware corporation (the \"Borrower\")."
        },
        {
            "query": "Who is the Lender in the agreement?",
            "answer": "GLOBAL BANK, a national banking association (the \"Lender\")."
        },
        {
            "query": "What is the Applicable Rate?",
            "answer": "\"Applicable Rate\" means 3.50% per annum."
        },
        {
            "query": "What is the Maturity Date?",
            "answer": "\"Maturity Date\" means May 15, 2030."
        },
        {
            "query": "What is a Business Day?",
            "answer": "\"Business Day\" means any day other than a Saturday, Sunday or other day on which commercial banks are authorized to close under the Laws of, or are in fact closed in, the state where the Lender's office is located."
        },
        {
            "query": "What happens if a payment is late?",
            "answer": "If any amount of principal of the Loan is not paid when due, whether at stated maturity, by acceleration or otherwise, such amount shall thereafter bear interest at a fluctuating interest rate per annum at all times equal to the Default Rate to the fullest extent permitted by applicable Laws."
        },
        {
            "query": "What is GAAP?",
            "answer": "\"GAAP\" means generally accepted accounting principles in the United States set forth in the opinions and pronouncements of the Accounting Principles Board and the American Institute of Certified Public Accountants and statements and pronouncements of the Financial Accounting Standards Board, consistently applied."
        }
    ]


def main():
    """Main function demonstrating semantic evaluation"""
    # Check if test file exists
    script_dir = os.path.dirname(os.path.abspath(__file__))
    test_file_dir = os.path.join(script_dir, "test_file")
    
    # Find available test files
    pdf_files = [f for f in os.listdir(test_file_dir) if f.endswith('.pdf')]
    txt_files = [f for f in os.listdir(test_file_dir) if f.endswith('.txt')]
    
    if pdf_files:
        test_file = os.path.join(test_file_dir, pdf_files[0])
        print(f"Using PDF file: {test_file}")
        full_text, _ = extract_text_from_pdf(test_file)
    elif txt_files:
        test_file = os.path.join(test_file_dir, txt_files[0])
        print(f"Using text file: {test_file}")
        with open(test_file, 'r', encoding='utf-8') as f:
            full_text = f.read()
    else:
        print(f"No test files found. Please add a PDF or text file to {test_file_dir}")
        return
    
    # Create chunking strategies
    strategies = [
        FixedSizeChunker(chunk_size=500, chunk_overlap=50),
        SentenceChunker(sentences_per_chunk=3),
        RollingWindowChunker(window_size=3, step_size=1),
        HybridSectionChunker(),
        ProductionChunker(sentences_per_chunk=3, min_chunk_char_length=50),
    ]
    
    # Add semantic chunking if available
    if has_transformers:
        strategies.append(SemanticChunker())
    
    # Create ground truth data
    ground_truth_data = create_legal_ground_truth_data()
    
    # Create evaluator
    evaluator = SemanticEvaluator()
    
    # Run evaluation
    print("\nRunning semantic evaluation against ground truth data...")
    
    # Get user input for retrieval method
    try:
        print("\nPlease select retrieval method for evaluation:")
        print("1. BM25 (lexical search)")
        print("2. Semantic (embedding-based search)")
        print("3. Hybrid (combined BM25 and semantic)")
        choice = input("Enter choice [default=3]: ").strip()
        
        if choice == "1":
            retrieval_method = "bm25"
        elif choice == "2":
            retrieval_method = "semantic"
        else:
            retrieval_method = "hybrid"
        
        # Run evaluation with selected method
        evaluation_results = evaluator.evaluate_against_ground_truth(
            strategies=strategies,
            full_text=full_text,
            ground_truth_data=ground_truth_data,
            retrieval_method=retrieval_method,
            top_k=3
        )
        
        # Save results
        evaluator.save_evaluation_results(evaluation_results)
        
        # Print summary
        print("\nEvaluation Summary:")
        print("=" * 60)
        print(f"{'Strategy':<30} {'Avg Best Sim':<15} {'Avg Avg Sim':<15}")
        print("-" * 60)
        
        # Sort strategies by best similarity score
        sorted_strategies = sorted(
            evaluation_results["summary"].items(),
            key=lambda x: x[1]["avg_best_similarity"],
            reverse=True
        )
        
        for strategy, scores in sorted_strategies:
            print(f"{strategy:<30} {scores['avg_best_similarity']:<15.4f} {scores['avg_avg_similarity']:<15.4f}")
        
    except KeyboardInterrupt:
        print("\nEvaluation interrupted by user.")
    except Exception as e:
        print(f"Error during evaluation: {e}")


if __name__ == "__main__":
    main() 