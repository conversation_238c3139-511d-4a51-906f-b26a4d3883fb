"""
Example script that demonstrates integrating the hybrid chunking strategy
with the existing retrieval module.

This script shows how to create chunks using the hybrid strategy
and then use them for RAG retrieval.
"""

import os
import sys
import json
from typing import List, Dict, Any

# Add parent directory to path to import from main project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# For testing with actual text file if PDF is not available
def read_text_file(file_path: str) -> str:
    """Read text from a file if PDF is not available"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"Error reading text file: {e}")
        return ""

try:
    # Import the chunking strategies from our module
    from chunking_test.chunking_test import (
        extract_text_from_pdf,
        HybridSectionChunker,
        RollingWindowChunker,
        SentenceChunker,
        ProductionChunker
    )
except ImportError:
    # Try alternative import path
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    try:
        from chunking_test import (
            extract_text_from_pdf,
            HybridSectionChunker,
            RollingWindowChunker,
            SentenceChunker,
            ProductionChunker
        )
    except ImportError:
        print("Error: Could not import chunking strategies. Make sure chunking_test.py is in the current directory.")
        sys.exit(1)

# Import from the main project
try:
    from src.keyword_code.rag.retrieval import retrieve_relevant_chunks
    from sentence_transformers import SentenceTransformer
    MAIN_PROJECT_AVAILABLE = True
except ImportError:
    print("Warning: Main project modules not available. Will only demonstrate chunking.")
    MAIN_PROJECT_AVAILABLE = False


def prepare_chunks_for_retrieval(chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Prepare chunks from our custom chunker to match the format expected by retrieval.py"""
    retrieval_chunks = []

    for i, chunk in enumerate(chunks):
        retrieval_chunks.append({
            "chunk_id": chunk.get("chunk_id", f"chunk_{i}"),
            "text": chunk["text"],
            "page_num": chunk.get("page_num", 0),
            # Include any other metadata from the chunk
            "token_count": chunk.get("token_count", 0),
            "section_type": chunk.get("section_type", "unknown")
        })

    return retrieval_chunks


def test_with_query(test_file: str, query: str, top_k: int = 3):
    """
    Test the hybrid chunking strategy with a query using the retrieval module.
    """
    # Extract text from PDF or text file
    print(f"Processing file: {test_file}")
    if test_file.endswith('.pdf'):
        full_text, _ = extract_text_from_pdf(test_file)
    else:
        full_text = read_text_file(test_file)

    if not full_text:
        print("Failed to extract text from file.")
        return

    # Create chunks using our hybrid chunking strategy
    print("Creating chunks using hybrid strategy...")
    chunker = HybridSectionChunker()
    chunks = chunker.create_chunks(full_text)

    print(f"Created {len(chunks)} chunks.")

    # Save the chunks to a file for reference
    output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "results")
    os.makedirs(output_dir, exist_ok=True)

    with open(os.path.join(output_dir, "hybrid_chunks_for_retrieval.json"), "w") as f:
        json.dump(chunks, f, indent=2)

    if not MAIN_PROJECT_AVAILABLE:
        print("Main project modules not available. Skipping retrieval test.")
        print(f"Chunks have been saved to {os.path.join(output_dir, 'hybrid_chunks_for_retrieval.json')}")
        return

    # Prepare chunks for retrieval
    retrieval_chunks = prepare_chunks_for_retrieval(chunks)

    # Load the embedding model
    print("Loading embedding model...")
    embedding_model = SentenceTransformer("all-MiniLM-L6-v2")

    # Perform retrieval
    print(f"Performing retrieval for query: '{query}'")
    results = retrieve_relevant_chunks(
        prompt=query,
        chunks=retrieval_chunks,
        model=embedding_model,
        top_k=top_k
    )

    # Print results
    print(f"\nTop {len(results)} results for query: '{query}'")
    for i, result in enumerate(results):
        print(f"\n--- Result {i+1} (Score: {result['score']:.4f}) ---")
        print(f"Section type: {result.get('section_type', 'N/A')}")
        print(f"Text: {result['text'][:200]}...")


def run_comparison_with_definition_query():
    """
    Run a comparison between different chunking strategies for a definition query.
    This shows how the hybrid strategy is better at capturing definitions.
    """
    # Check if test file exists
    test_file_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_file")
    pdf_files = [f for f in os.listdir(test_file_dir) if f.endswith('.pdf')]
    txt_files = [f for f in os.listdir(test_file_dir) if f.endswith('.txt')]

    if pdf_files:
        test_file = os.path.join(test_file_dir, pdf_files[0])
        full_text, _ = extract_text_from_pdf(test_file)
    elif txt_files:
        test_file = os.path.join(test_file_dir, txt_files[0])
        full_text = read_text_file(test_file)
    else:
        print(f"No test files found in {test_file_dir}. Please add a test.pdf or text file.")
        return

    if not full_text:
        print("Failed to extract text from file.")
        return

    # Query that would look for a definition
    definition_query = "What is the definition of Dollars?"

    # Create chunks using different strategies
    hybrid_chunker = HybridSectionChunker()
    sentence_chunker = SentenceChunker(sentences_per_chunk=3)  # Standard 3-sentence approach
    rolling_chunker = RollingWindowChunker(window_size=3, step_size=1)
    production_chunker = ProductionChunker(sentences_per_chunk=3, min_chunk_char_length=50)  # Production strategy

    hybrid_chunks = hybrid_chunker.create_chunks(full_text)
    sentence_chunks = sentence_chunker.create_chunks(full_text)
    rolling_chunks = rolling_chunker.create_chunks(full_text)
    production_chunks = production_chunker.create_chunks(full_text)

    if not MAIN_PROJECT_AVAILABLE:
        print("Main project modules not available. Skipping retrieval comparison.")
        return

    # Prepare chunks for retrieval
    hybrid_retrieval_chunks = prepare_chunks_for_retrieval(hybrid_chunks)
    sentence_retrieval_chunks = prepare_chunks_for_retrieval(sentence_chunks)
    rolling_retrieval_chunks = prepare_chunks_for_retrieval(rolling_chunks)
    production_retrieval_chunks = prepare_chunks_for_retrieval(production_chunks)

    # Load the embedding model
    embedding_model = SentenceTransformer("all-MiniLM-L6-v2")

    # Perform retrieval with each strategy
    print(f"\nComparing chunking strategies for query: '{definition_query}'")

    print("\n--- HYBRID CHUNKING STRATEGY ---")
    hybrid_results = retrieve_relevant_chunks(
        prompt=definition_query,
        chunks=hybrid_retrieval_chunks,
        model=embedding_model,
        top_k=1
    )

    if hybrid_results:
        result = hybrid_results[0]
        print(f"Score: {result['score']:.4f}")
        print(f"Section type: {result.get('section_type', 'N/A')}")
        print(f"Text: {result['text']}")

    print("\n--- STANDARD SENTENCE CHUNKING ---")
    sentence_results = retrieve_relevant_chunks(
        prompt=definition_query,
        chunks=sentence_retrieval_chunks,
        model=embedding_model,
        top_k=1
    )

    if sentence_results:
        result = sentence_results[0]
        print(f"Score: {result['score']:.4f}")
        print(f"Text: {result['text']}")

    print("\n--- ROLLING WINDOW CHUNKING ---")
    rolling_results = retrieve_relevant_chunks(
        prompt=definition_query,
        chunks=rolling_retrieval_chunks,
        model=embedding_model,
        top_k=1
    )

    if rolling_results:
        result = rolling_results[0]
        print(f"Score: {result['score']:.4f}")
        print(f"Text: {result['text']}")

    print("\n--- PRODUCTION CHUNKING STRATEGY ---")
    production_results = retrieve_relevant_chunks(
        prompt=definition_query,
        chunks=production_retrieval_chunks,
        model=embedding_model,
        top_k=1
    )

    if production_results:
        result = production_results[0]
        print(f"Score: {result['score']:.4f}")
        print(f"Text: {result['text']}")


if __name__ == "__main__":
    # Check if test file exists
    test_file_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_file")
    pdf_files = [f for f in os.listdir(test_file_dir) if f.endswith('.pdf')]
    txt_files = [f for f in os.listdir(test_file_dir) if f.endswith('.txt')]

    if pdf_files:
        test_file = os.path.join(test_file_dir, pdf_files[0])
    elif txt_files:
        test_file = os.path.join(test_file_dir, txt_files[0])
    else:
        print(f"No test files found in {test_file_dir}. Please add a test.pdf or text file.")
        sys.exit(1)

    # Example query that might look for a definition
    test_with_query(test_file, "What is the definition of Dollars?", top_k=3)

    # Run a comparison of different strategies
    print("\n" + "="*60)
    print("RUNNING CHUNKING STRATEGY COMPARISON")
    print("="*60)
    run_comparison_with_definition_query()