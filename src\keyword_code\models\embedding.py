"""
Embedding model loading and management.
"""

import streamlit as st
from ..config import logger

# Import Databricks embedding model
from .databricks_embedding import load_databricks_embedding_model


@st.cache_resource  # Use cache_resource for non-data objects like models
def load_embedding_model():
    """
    Loads the Databricks embedding model and caches it.

    Note: This function is cached using Streamlit's cache_resource decorator
    to avoid recreating the client on each Streamlit rerun, which is a
    performance optimization.
    """
    logger.info("Loading Databricks embedding model...")
    databricks_model = load_databricks_embedding_model()

    if databricks_model is not None:
        logger.info("Successfully loaded Databricks embedding model")
        return databricks_model
    else:
        error_msg = "Failed to load Databricks embedding model. Please check your DATABRICKS_API_KEY in .env file."
        logger.error(error_msg)
        st.error(f"Fatal Error: {error_msg}")
        return None



