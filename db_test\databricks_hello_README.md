# Databricks Llama Model Test Script

This is a simple test script to send a "hello" message to the Databricks Meta Llama 3 model and display the response in the terminal.

## Prerequisites

- Python 3.8 or higher
- OpenAI Python package
- A valid Databricks API token

## Installation

1. Make sure you have the OpenAI package installed:

```bash
pip install openai
```

## Usage

### Option 1: Run the Python script directly

1. Set the DATABRICKS_TOKEN environment variable:

   **Windows CMD:**
   ```
   set DATABRICKS_TOKEN=your_token_here
   ```

   **Windows PowerShell:**
   ```
   $env:DATABRICKS_TOKEN='your_token_here'
   ```

   **Linux/Mac:**
   ```
   export DATABRICKS_TOKEN=your_token_here
   ```

2. Run the Python script:

   ```
   python databricks_hello.py
   ```

### Option 2: Use the batch script (Windows CMD)

1. Run the batch script:

   ```
   run_databricks_hello.bat
   ```

   Or provide the token as an argument:

   ```
   run_databricks_hello.bat your_token_here
   ```

### Option 3: Use the PowerShell script (Windows PowerShell)

1. Run the PowerShell script:

   ```
   .\run_databricks_hello.ps1
   ```

   Or provide the token as an argument:

   ```
   .\run_databricks_hello.ps1 your_token_here
   ```

## What the Script Does

1. Connects to the Databricks endpoint using the provided token
2. Sends a simple "Hello! How are you today?" message to the Llama 3 model
3. Displays the model's response in the terminal

## Troubleshooting

- If you get an error about the DATABRICKS_TOKEN not being found, make sure you've set the environment variable correctly.
- If you get an authentication error, verify that your Databricks token is valid.
- If you get an error about the OpenAI module not being found, make sure you've installed it with `pip install openai`.

## Cleanup

These are temporary test scripts. You can delete them after testing:
- databricks_hello.py
- run_databricks_hello.bat
- run_databricks_hello.ps1
- databricks_hello_README.md
