@echo off
REM Script to run the Databricks embedding test with the token

REM Prompt for the Databricks token if not provided as an argument
if "%1"=="" (
    set /p DATABRICKS_TOKEN="Enter your Databricks token: "
) else (
    set DATABRICKS_TOKEN=%1
)

REM Run the Python script with the token set as an environment variable
echo Running databricks_embedding.py...
python databricks_embedding.py

REM Pause to see the output
pause
